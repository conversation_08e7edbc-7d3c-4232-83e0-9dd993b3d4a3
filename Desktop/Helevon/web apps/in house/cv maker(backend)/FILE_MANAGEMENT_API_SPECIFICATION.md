# File Management API Specification - CV System

## Overview

This document provides the API specification for file management endpoints in the CV system. The system handles profile photos and certificates as direct file uploads stored in the database as base64-encoded data.

## Base Configuration

- **Base URL**: `{BACKEND_BASE_URL}/api/v1`
- **Authentication**: JWT Bearer token required for all endpoints
- **File Storage**: Files are stored directly in the database as base64-encoded data

---

## 1. Upload Profile Photo

### Endpoint Details
| Property | Value |
|----------|-------|
| **Route** | `/cv/{cv_id}/photo` |
| **HTTP Method** | `POST` |
| **Description** | Upload profile photo for a CV (replaces existing photo) |

### Request Headers
| Header | Value | Required |
|--------|-------|----------|
| `Authorization` | `Bearer {access_token}` | ✅ |
| `Content-Type` | `multipart/form-data` | ✅ |

### Request Parameters
| Parameter | Type | Location | Required | Description |
|-----------|------|----------|----------|-------------|
| `cv_id` | `string` | Path | ✅ | CV unique identifier |
| `file` | `File` | Form Data | ✅ | Photo file (JPG, PNG only, max 5MB) |

### File Requirements
| Property | Requirement |
|----------|-------------|
| **Formats** | JPG, PNG only |
| **Max Size** | 5MB |
| **Min Dimensions** | 50x50 pixels |
| **Max Dimensions** | 4000x4000 pixels |

### Response Format
```json
{
  "id": "string",
  "name": "string",
  "type": "string",
  "size": "number",
  "category": "photo",
  "url": "/api/v1/files/{file_id}",
  "created_at": "string (ISO 8601)",
  "updated_at": "string (ISO 8601)"
}
```

### Status Codes
| Code | Description |
|------|-------------|
| `201` | Photo uploaded successfully |
| `400` | Invalid file format, size, or validation error |
| `401` | Invalid or missing authentication token |
| `404` | CV not found |
| `500` | Upload failed |

---

## 2. Upload Certificate

### Endpoint Details
| Property | Value |
|----------|-------|
| **Route** | `/cv/{cv_id}/education/{education_id}/certificate` |
| **HTTP Method** | `POST` |
| **Description** | Upload certificate for a specific education entry |

### Request Headers
| Header | Value | Required |
|--------|-------|----------|
| `Authorization` | `Bearer {access_token}` | ✅ |
| `Content-Type` | `multipart/form-data` | ✅ |

### Request Parameters
| Parameter | Type | Location | Required | Description |
|-----------|------|----------|----------|-------------|
| `cv_id` | `string` | Path | ✅ | CV unique identifier |
| `education_id` | `string` | Path | ✅ | Education entry identifier |
| `file` | `File` | Form Data | ✅ | Certificate file (PDF, JPG, PNG only, max 5MB) |

### File Requirements
| Property | Requirement |
|----------|-------------|
| **Formats** | PDF, JPG, PNG only |
| **Max Size** | 5MB |
| **Purpose** | Certificate or diploma document |

### Response Format
```json
{
  "id": "string",
  "name": "string",
  "type": "string",
  "size": "number",
  "category": "certificate",
  "url": "/api/v1/files/{file_id}",
  "created_at": "string (ISO 8601)",
  "updated_at": "string (ISO 8601)"
}
```

### Status Codes
| Code | Description |
|------|-------------|
| `201` | Certificate uploaded successfully |
| `400` | Invalid file format, size, or validation error |
| `401` | Invalid or missing authentication token |
| `404` | CV or education entry not found |
| `500` | Upload failed |

---

## 3. Get File Content

### Endpoint Details
| Property | Value |
|----------|-------|
| **Route** | `/files/{file_id}` |
| **HTTP Method** | `GET` |
| **Description** | Retrieve file content (photo or certificate) |

### Request Headers
| Header | Value | Required |
|--------|-------|----------|
| `Authorization` | `Bearer {access_token}` | ✅ |

### Request Parameters
| Parameter | Type | Location | Required | Description |
|-----------|------|----------|----------|-------------|
| `file_id` | `string` | Path | ✅ | File unique identifier |

### Response Format
- **Content-Type**: Original file MIME type (`image/jpeg`, `image/png`, `application/pdf`)
- **Body**: Binary file data

### Response Headers
| Header | Value |
|--------|-------|
| `Content-Type` | Original file MIME type |
| `Content-Disposition` | `inline; filename="{original_filename}"` |
| `Content-Length` | `{size_in_bytes}` |
| `Cache-Control` | `private, max-age=3600` |

### Status Codes
| Code | Description |
|------|-------------|
| `200` | File retrieved successfully |
| `401` | Invalid or missing authentication token |
| `404` | File not found |
| `500` | File retrieval failed or data corrupted |

---

## 4. Delete File

### Endpoint Details
| Property | Value |
|----------|-------|
| **Route** | `/files/{file_id}` |
| **HTTP Method** | `DELETE` |
| **Description** | Delete a file (photo or certificate) |

### Request Headers
| Header | Value | Required |
|--------|-------|----------|
| `Authorization` | `Bearer {access_token}` | ✅ |

### Request Parameters
| Parameter | Type | Location | Required | Description |
|-----------|------|----------|----------|-------------|
| `file_id` | `string` | Path | ✅ | File unique identifier |

### Response Format
```json
{
  "message": "File deleted successfully",
  "file_id": "string"
}
```

### Status Codes
| Code | Description |
|------|-------------|
| `200` | File deleted successfully |
| `401` | Invalid or missing authentication token |
| `404` | File not found |
| `500` | Deletion failed |

---

## 5. General File Upload (Legacy)

### Endpoint Details
| Property | Value |
|----------|-------|
| **Route** | `/cv/{cv_id}/upload` |
| **HTTP Method** | `POST` |
| **Description** | General file upload with category specification |

### Request Headers
| Header | Value | Required |
|--------|-------|----------|
| `Authorization` | `Bearer {access_token}` | ✅ |
| `Content-Type` | `multipart/form-data` | ✅ |

### Request Parameters
| Parameter | Type | Location | Required | Description |
|-----------|------|----------|----------|-------------|
| `cv_id` | `string` | Path | ✅ | CV unique identifier |
| `file` | `File` | Form Data | ✅ | File to upload |
| `category` | `string` | Form Data | ✅ | File category ("photo", "certificate", "cover_letter", "other") |

### Response Format
```json
{
  "id": "string",
  "name": "string",
  "type": "string",
  "size": "number",
  "category": "string",
  "url": "/api/v1/files/{file_id}",
  "created_at": "string (ISO 8601)",
  "updated_at": "string (ISO 8601)"
}
```

---

## File Integration with CV Data

### Profile Photo Integration
When a profile photo is uploaded:
1. The file is stored in the database with `category: "photo"`
2. The CV's `personal_info.photoUrl` field is updated with the file ID
3. Only one profile photo per CV is allowed (new uploads replace existing)

### Certificate Integration
When a certificate is uploaded:
1. The file is stored in the database with `category: "certificate"`
2. The file ID is added to the education entry's `certificates` array
3. Multiple certificates per education entry are supported

### CV Data Structure
```json
{
  "personal_info": {
    "firstName": "string",
    "lastName": "string",
    "photoUrl": "file_id_string_or_null"
  },
  "education": [
    {
      "id": "string",
      "institution": "string",
      "degree": "string",
      "certificates": ["file_id_1", "file_id_2"]
    }
  ]
}
```

---

## Error Response Format

All endpoints return errors in the following format:

```json
{
  "detail": "string"
}
```

For validation errors, additional details may be included:

```json
{
  "detail": "File validation failed",
  "errors": [
    {
      "field": "file",
      "message": "File size exceeds maximum allowed size"
    }
  ]
}
```

---

## Usage Examples

### Upload Profile Photo
```
POST /api/v1/cv/123e4567-e89b-12d3-a456-426614174000/photo
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: multipart/form-data

file: [binary photo data - JPG/PNG]
```

### Upload Certificate
```
POST /api/v1/cv/123e4567-e89b-12d3-a456-426614174000/education/edu_123/certificate
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: multipart/form-data

file: [binary certificate data - PDF/JPG/PNG]
```

### View Uploaded Photo
```
GET /api/v1/files/file_456
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Delete File
```
DELETE /api/v1/files/file_456
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

---

## Security Features

1. **File Type Validation**: Magic number detection prevents file type spoofing
2. **Size Limits**: 5MB maximum file size
3. **Content Scanning**: Detection of embedded scripts and malicious content
4. **Filename Sanitization**: Protection against path traversal attacks
5. **User Ownership**: Files can only be accessed by their owners
6. **Secure Storage**: Files stored as base64 in database, not filesystem

---

## Notes for Frontend Developers

1. **File Uploads**: Use `FormData` with `multipart/form-data` content type
2. **File Display**: Use the `/files/{file_id}` endpoint to display images/PDFs
3. **Error Handling**: Check status codes and handle validation errors appropriately
4. **File Replacement**: Profile photo uploads automatically replace existing photos
5. **Multiple Certificates**: Education entries can have multiple certificates
6. **File IDs**: Store file IDs returned from upload endpoints for later reference
7. **Caching**: File content is cached for 1 hour for performance
