#!/usr/bin/env python3
"""
Test script to verify all the fixes are working correctly.

This script tests:
1. CV retrieval with cover letter parsing
2. CV creation without user_id
3. PDF export with TA_JUSTIFY import
4. File upload endpoints
5. Schema validations
"""

import sys
import os
import json

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))

def test_schema_imports():
    """Test that all schemas import correctly."""
    print("🧪 Testing Schema Imports...")
    
    try:
        from app.schemas.cv import (
            CVCreate, CVResponse, PersonalInfoUpdate, 
            SkillCreate, SkillUpdate, CoverLetterData, CoverLetterUpdate
        )
        print("✅ CV schemas imported successfully")
        
        from app.schemas.file import FileResponse, FileWithData
        print("✅ File schemas imported successfully")
        
        # Test CVCreate without user_id
        cv_data = {
            "title": "Test CV",
            "template": "german",
            "language": "de"
        }
        cv_create = CVCreate(**cv_data)
        print("✅ CVCreate works without user_id")
        
        # Test cover letter schema
        cover_letter_data = {
            "recipientName": "Test Recipient",
            "company": "Test Company",
            "content": "Test content",
            "date": "2025-01-01"
        }
        cover_letter = CoverLetterData(**cover_letter_data)
        print("✅ CoverLetterData schema works")
        
        # Test skill creation without ID
        skill_data = {
            "name": "Python",
            "category": "technical",
            "level": "expert"
        }
        skill_create = SkillCreate(**skill_data)
        print("✅ SkillCreate works without ID")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema import error: {e}")
        return False


def test_pdf_imports():
    """Test that PDF service imports correctly."""
    print("\n🧪 Testing PDF Service Imports...")
    
    try:
        from app.services.pdf_service import PDFService
        print("✅ PDFService imported successfully")
        
        # Test that TA_JUSTIFY is available
        from reportlab.lib.enums import TA_JUSTIFY
        print("✅ TA_JUSTIFY imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ PDF import error: {e}")
        return False


def test_file_endpoint_imports():
    """Test that file endpoints import correctly."""
    print("\n🧪 Testing File Endpoint Imports...")
    
    try:
        from app.endpoints.files import upload_profile_photo, upload_certificate
        print("✅ File endpoints imported successfully")
        
        # Test SQLAlchemy and_ import
        from sqlalchemy import and_
        print("✅ SQLAlchemy and_ imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ File endpoint import error: {e}")
        return False


def test_cv_response_parsing():
    """Test CV response with cover letter parsing."""
    print("\n🧪 Testing CV Response Parsing...")
    
    try:
        from app.schemas.cv import CVResponse
        
        # Test with JSON string cover letter (current database format)
        cv_data = {
            "id": "test-id",
            "user_id": "test-user",
            "title": "Test CV",
            "template": "german",
            "language": "de",
            "personal_info": {},
            "education": [],
            "work_experience": [],
            "skills": [],
            "references": [],
            "cover_letter": '{"recipientName": "Test", "content": "Test content"}',  # JSON string
            "created_at": "2025-01-01T00:00:00",
            "updated_at": "2025-01-01T00:00:00"
        }
        
        cv_response = CVResponse(**cv_data)
        print("✅ CVResponse accepts JSON string cover letter")
        
        # Test with dict cover letter
        cv_data["cover_letter"] = {"recipientName": "Test", "content": "Test content"}
        cv_response = CVResponse(**cv_data)
        print("✅ CVResponse accepts dict cover letter")
        
        # Test with None cover letter
        cv_data["cover_letter"] = None
        cv_response = CVResponse(**cv_data)
        print("✅ CVResponse accepts None cover letter")
        
        return True
        
    except Exception as e:
        print(f"❌ CV response parsing error: {e}")
        return False


def test_file_response_schema():
    """Test FileResponse schema with all required fields."""
    print("\n🧪 Testing FileResponse Schema...")
    
    try:
        from app.schemas.file import FileResponse
        
        file_data = {
            "id": "test-file-id",
            "user_id": "test-user-id",
            "cv_id": "test-cv-id",
            "name": "test.jpg",
            "type": "image/jpeg",
            "size": 1024,
            "category": "photo",
            "url": "/api/v1/files/test-file-id",
            "created_at": "2025-01-01T00:00:00",
            "updated_at": "2025-01-01T00:00:00"
        }
        
        file_response = FileResponse(**file_data)
        print("✅ FileResponse schema works with all fields")
        
        return True
        
    except Exception as e:
        print(f"❌ FileResponse schema error: {e}")
        return False


def test_skill_auto_id_logic():
    """Test the skill auto-ID generation logic."""
    print("\n🧪 Testing Skill Auto-ID Logic...")
    
    try:
        import uuid
        from app.schemas.cv import SkillEntry
        
        # Test skill without ID (should work)
        skill_data = {
            "name": "Python",
            "category": "technical", 
            "level": "expert"
        }
        skill = SkillEntry(**skill_data)
        print("✅ SkillEntry works without ID")
        
        # Test skill with ID (should also work)
        skill_data["id"] = str(uuid.uuid4())
        skill = SkillEntry(**skill_data)
        print("✅ SkillEntry works with ID")
        
        # Test auto-generation logic (simulating endpoint logic)
        skills_data = [
            {"name": "Python", "category": "technical", "level": "expert"},
            {"name": "JavaScript", "category": "technical", "level": "advanced", "id": "existing-id"}
        ]
        
        skills_list = []
        for skill_dict in skills_data:
            if not skill_dict.get('id'):
                skill_dict['id'] = str(uuid.uuid4())
            skills_list.append(skill_dict)
        
        print(f"✅ Auto-generated {len([s for s in skills_list if s['name'] == 'Python'])} skill IDs")
        print(f"✅ Preserved {len([s for s in skills_list if s['name'] == 'JavaScript'])} existing IDs")
        
        return True
        
    except Exception as e:
        print(f"❌ Skill auto-ID logic error: {e}")
        return False


def test_cover_letter_parsing():
    """Test cover letter parsing logic."""
    print("\n🧪 Testing Cover Letter Parsing...")
    
    try:
        from app.services.pdf_service import PDFService
        
        pdf_service = PDFService()
        
        # Test JSON string parsing
        json_cover_letter = '{"recipientName": "Test", "content": "Test content", "date": "2025-01-01"}'
        parsed = pdf_service._parse_cover_letter(json_cover_letter)
        print("✅ JSON string cover letter parsed")
        
        # Test dict parsing
        dict_cover_letter = {"recipientName": "Test", "content": "Test content"}
        parsed = pdf_service._parse_cover_letter(dict_cover_letter)
        print("✅ Dict cover letter parsed")
        
        # Test None parsing
        parsed = pdf_service._parse_cover_letter(None)
        assert parsed is None
        print("✅ None cover letter handled")
        
        # Test plain string parsing
        plain_cover_letter = "Simple text content"
        parsed = pdf_service._parse_cover_letter(plain_cover_letter)
        assert parsed['content'] == plain_cover_letter
        print("✅ Plain string cover letter parsed")
        
        return True
        
    except Exception as e:
        print(f"❌ Cover letter parsing error: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 Testing All Fixes")
    print("=" * 50)
    
    tests = [
        test_schema_imports,
        test_pdf_imports,
        test_file_endpoint_imports,
        test_cv_response_parsing,
        test_file_response_schema,
        test_skill_auto_id_logic,
        test_cover_letter_parsing
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes are working correctly!")
        print("\n✅ Ready to test:")
        print("  - CV retrieval (cover letter parsing)")
        print("  - CV creation (no user_id required)")
        print("  - PDF export (TA_JUSTIFY available)")
        print("  - Profile photo upload (and_ imported)")
        print("  - Certificate upload (FileResponse complete)")
        print("  - Skills auto-ID generation")
        print("  - Cover letter comprehensive structure")
        return 0
    else:
        print(f"❌ {total - passed} tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    exit(main())
