# German CV Template - Backend Implementation Specification

## Overview
This document provides comprehensive specifications for implementing the German CV template on the backend. The template generates a professional, multi-page PDF document following German CV standards.

## Template Identification
- **Template ID**: `german`
- **Template Name**: "German Template"
- **Language**: German (de)
- **Target Use**: Professional German CVs for all industries (not limited to Ausbildung)

## Document Structure

### Page Layout
- **Page Size**: A4 (595.28 × 841.89 points)
- **Margins**: 40 points (approximately 14mm) on all sides
- **Font Family**: Helvetica (Light, Normal, Bold weights)
- **Base Font Size**: 11pt
- **Color Scheme**: 
  - Primary: #005A9C (Professional blue)
  - Text: #333333 (Dark gray)
  - Secondary text: #444444
  - Borders/Lines: #CCCCCC

### Document Pages (in order)

#### 1. Cover Page (Deckblatt)
**Layout Elements:**
- Centered content with professional photo (if provided)
- Application subject line (from cover letter if available)
- Horizontal divider line
- Applicant's full name
- Footer with "Bewerbu<PERSON>sunterlagen | [Current Date]"

**Photo Specifications:**
- Size: 120×120 points (circular)
- Position: Center-top of page
- Format: Any image format supported by backend

**Typography:**
- Subject: 24pt, bold, centered
- Name: 20pt, bold, centered
- Footer: 11pt, normal

#### 2. Table of Contents (Inhaltsverzeichnis)
**Required Sections:**
- 1. Lebenslauf
  - 1.1 Bildung
  - 1.2 Berufserfahrung  
  - 1.3 Kenntnisse und Fähigkeiten
  - 1.4 Referenzen (if references exist)
- 2. Anschreiben (if cover letter exists)
- 3. Zertifikate (if certificates exist)

**Typography:**
- Title: 16pt, bold
- Items: 12pt, normal, with clickable links

#### 3. CV Main Page (Lebenslauf)
**Section Order:**
1. Personal Information (Persönliche Daten)
2. Education (Bildung)
3. Work Experience (Berufserfahrung)
4. Skills (Kenntnisse und Fähigkeiten)
5. References (Referenzen)

### Data Field Specifications

#### Personal Information Section
**Layout**: Two-column format (30% labels, 70% values)

**Required Fields:**
- Name: `firstName lastName`
- Adresse: `address, postalCode city`
- Telefon: `phone`
- E-Mail: `email`

**Optional Fields:**
- Geburtsdatum: `dateOfBirth` (if provided)
- Nationalität: `nationality` (if provided)

**Typography:**
- Labels: Bold, #444444
- Values: Normal, #333333

#### Education Section (Bildung)
**Layout**: Two-column format (25% dates, 75% details)

**Data Structure per Entry:**
```json
{
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD" | null,
  "current": boolean,
  "degree": "string",
  "institution": "string", 
  "description": "string",
  "certificateUrl": "string" | null
}
```

**Display Format:**
- Dates: "MM/YYYY - MM/YYYY" or "MM/YYYY - Heute" (if current)
- Degree: Bold, #005A9C, 11pt
- Institution: Normal, 11pt
- Description: 10pt, #444444
- Certificate indicator: "Zertifikat: [filename]" if certificateUrl exists

**Sorting**: Most recent first (by endDate, current entries first)

#### Work Experience Section (Berufserfahrung)
**Layout**: Two-column format (25% dates, 75% details)

**Data Structure per Entry:**
```json
{
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD" | null,
  "current": boolean,
  "position": "string",
  "company": "string",
  "description": "string"
}
```

**Display Format:**
- Dates: "MM/YYYY - MM/YYYY" or "MM/YYYY - Heute" (if current)
- Position: Bold, #005A9C, 11pt
- Company: Normal, 11pt
- Description: 10pt, #444444

**Sorting**: Most recent first (by endDate, current entries first)

#### Skills Section (Kenntnisse und Fähigkeiten)
**Categories** (in order):
1. Technisch (Technical)
2. Sprache (Language) 
3. Soft Skills

**Data Structure per Skill:**
```json
{
  "name": "string",
  "category": "technical" | "language" | "soft",
  "level": 1-5 (integer)
}
```

**Display Format:**
- Category headers: 12pt, bold, #005A9C, with underline
- Skill name: 11pt, normal
- Level indicator: 5 dots (filled: #005A9C, empty: #EEEEEE with border)
- Layout: Flexible row layout, multiple skills per row

**German Translations Required:**
- technical → "Technisch"
- language → "Sprache" 
- soft → "Soft Skills"

#### References Section (Referenzen)
**Data Structure per Reference:**
```json
{
  "name": "string",
  "position": "string", 
  "company": "string",
  "email": "string",
  "phone": "string"
}
```

**Display Format:**
- Name: Bold, #005A9C, 11pt
- Position: Normal, 11pt
- Contact: 10pt, #444444, format: "company | email | phone"

### Cover Letter Integration
If `coverLetter` field exists, add as separate page after CV.

**Data Structure:**
```json
{
  "recipientName": "string",
  "recipientAddress": "string", 
  "subject": "string",
  "content": "string",
  "salutation": "string",
  "closing": "string"
}
```

**Layout:**
- Sender info: Top-left, 8pt
- Recipient info: Left-aligned, 10pt
- Date: Right-aligned, 10pt
- Subject: Bold, 12pt, #005A9C
- Content: Justified text, 10pt, line height 1.5
- Closing: 10pt
- Signature space: 30pt margin

### Certificate Attachments
If education entries have `certificateUrl`, merge certificate PDFs after main CV pages.

**Requirements:**
- Download certificates from provided URLs
- Merge as additional pages in document
- Maintain original certificate formatting
- Add to table of contents as "3. Zertifikate"

## Translation Requirements

### Automatic Content Translation
The backend must translate the following content to German:

**Skill Categories:**
- "technical" → "Technisch"
- "language" → "Sprache"
- "soft" → "Soft Skills"

**Common Terms:**
- "present" → "Heute"
- Date formats: Use German format (DD.MM.YYYY)

**Technical Skills Translation Map:**
```json
{
  "javascript": "JavaScript",
  "typescript": "TypeScript", 
  "react": "React",
  "angular": "Angular",
  "vue": "Vue.js",
  "node": "Node.js",
  "python": "Python",
  "java": "Java",
  "csharp": "C#",
  "cpp": "C++",
  "php": "PHP",
  "sql": "SQL",
  "mongodb": "MongoDB",
  "postgresql": "PostgreSQL",
  "mysql": "MySQL"
}
```

**Language Skills Translation:**
```json
{
  "english": "Englisch",
  "german": "Deutsch", 
  "arabic": "Arabisch",
  "french": "Französisch",
  "spanish": "Spanisch",
  "italian": "Italienisch",
  "russian": "Russisch",
  "chinese": "Chinesisch",
  "japanese": "Japanisch"
}
```

**Soft Skills Translation:**
```json
{
  "teamwork": "Teamarbeit",
  "communication": "Kommunikation",
  "leadership": "Führungsqualitäten", 
  "problemsolving": "Problemlösung",
  "creativity": "Kreativität",
  "adaptability": "Anpassungsfähigkeit",
  "timemanagement": "Zeitmanagement"
}
```

## PDF Generation Requirements

### Document Metadata
```json
{
  "title": "Lebenslauf - [firstName] [lastName]",
  "author": "[firstName] [lastName]",
  "subject": "Lebenslauf / CV",
  "keywords": "Lebenslauf, CV, Resume, Bewerbung",
  "creator": "CV Application Maker",
  "producer": "CV Application Maker"
}
```

### Page Numbering
- Format: "X / Y" (current page / total pages)
- Position: Bottom-right
- Font: 10pt, normal
- Start from page 2 (skip cover page)

### Footer Content
- Page 1: "Bewerbungsunterlagen | [Current Date in DD.MM.YYYY]"
- Other pages: "Bewerbungsunterlagen | [firstName] [lastName]"

## Error Handling

### Missing Data Handling
- **No photo**: Skip photo section, maintain layout
- **No cover letter**: Skip cover letter page and table of contents entry
- **No certificates**: Skip certificate section
- **Empty sections**: Show section header with "Keine Angaben" message
- **Invalid dates**: Show as provided, don't format

### Image Processing
- **Invalid photo URL**: Skip photo, log error
- **Certificate download failure**: Skip certificate, log error
- **Unsupported image format**: Skip image, log error

## Performance Considerations
- **Font loading**: Cache Helvetica fonts
- **Image processing**: Resize images to required dimensions
- **PDF merging**: Stream processing for large certificates
- **Memory management**: Process large documents in chunks

This specification provides all necessary details for backend implementation of the German CV template with professional formatting and German language standards.
