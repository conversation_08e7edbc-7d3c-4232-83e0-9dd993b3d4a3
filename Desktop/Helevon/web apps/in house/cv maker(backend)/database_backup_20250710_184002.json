{"users": [{"id": "8d1dd76e-dff0-4ebe-bf52-ead65573e10f", "name": "<PERSON>", "email": "<EMAIL>", "email_verified": null, "password": "$2b$12$hdR8AT130xtwvbNHeT5ZD.j7JGY7DZEozYmxIhEJj6FW9byhAKikO", "image": null, "language": "de", "created_at": "2025-07-10 16:16:41", "updated_at": "2025-07-10 16:17:06", "last_login_at": "2025-07-10 16:17:06.601362", "login_attempts": 0, "locked_until": null, "refresh_token_hash": "f3ccad1cf8068217e8b187c86c5f0127e334895b482666c4b7cbc218dab7125d", "refresh_token_expires_at": "2025-07-17 16:17:06.664246"}], "cvs": [{"id": "fad8eb5e-1a52-4102-a0c7-0b5a96a566d7", "user_id": "8d1dd76e-dff0-4ebe-bf52-ead65573e10f", "title": "First CV", "template": "german", "language": "de", "personal_info": "{\"firstName\": \"<PERSON>\", \"lastName\": \"<PERSON><PERSON><PERSON>\", \"email\": \"<EMAIL>\", \"phone\": \"+44 7911 123456\", \"address\": \"123 Innovation Street\", \"city\": \"London\", \"postalCode\": \"E1 6AN\", \"country\": \"United Kingdom\", \"dateOfBirth\": \"1990-06-15\", \"placeOfBirth\": \"Manchester\", \"nationality\": \"British\", \"maritalStatus\": \"Single\", \"photoUrl\": null, \"summary\": \"Experienced software engineer with a passion for developing scalable web applications and working across the full stack.\"}", "education": "[{\"id\": \"3c4e8a36-1b3e-4f21-bf5f-9e3f97c0d120\", \"institution\": \"University of Oxford\", \"degree\": \"Bachelor of Science\", \"fieldOfStudy\": \"Computer Science\", \"startDate\": \"2008-09-01\", \"endDate\": \"2011-06-30\", \"isCurrentlyStudying\": false, \"grade\": \"First Class Honours\", \"description\": \"Focused on algorithms, distributed systems, and software engineering.\", \"certificates\": []}, {\"id\": \"7c8c40e9-0b6e-4c29-a0dc-2f6a72b8cb38\", \"institution\": \"Imperial College London\", \"degree\": \"Master of Science\", \"fieldOfStudy\": \"Artificial Intelligence\", \"startDate\": \"2012-09-01\", \"endDate\": \"2013-09-01\", \"isCurrentlyStudying\": false, \"grade\": \"Distinction\", \"description\": \"Specialized in deep learning and natural language processing.\", \"certificates\": []}, {\"id\": \"a9e2a4a4-648b-4dc4-9f15-cbe56f67b246\", \"institution\": \"Coursera / Stanford University\", \"degree\": \"Professional Certificate\", \"fieldOfStudy\": \"Machine Learning\", \"startDate\": \"2020-01-15\", \"endDate\": \"2020-05-15\", \"isCurrentlyStudying\": false, \"grade\": \"Completed\", \"description\": \"Online course led by Andrew Ng covering foundational ML concepts.\", \"certificates\": []}]", "work_experience": "[{\"id\": \"f2db1ee4-e6f1-4f08-8795-5e19b876fcd6\", \"company\": \"TechNova Solutions\", \"position\": \"Senior Software Engineer\", \"startDate\": \"2019-04-01\", \"endDate\": \"2024-01-31\", \"isCurrentlyWorking\": false, \"description\": \"Led a team of 5 developers to build scalable microservices for fintech applications.\", \"location\": \"Remote\"}, {\"id\": \"b8f3f973-f17e-42f0-9e62-46cf6f789bc5\", \"company\": \"InnovateX\", \"position\": \"Full Stack Developer\", \"startDate\": \"2015-06-01\", \"endDate\": \"2019-03-31\", \"isCurrentlyWorking\": false, \"description\": \"Worked on cross-platform web apps using React and Django. Introduced CI/CD pipelines that reduced deployment time by 40%.\", \"location\": \"Manchester, UK\"}, {\"id\": \"37a2cf94-951b-4b4e-9f5e-803c13c6f2a7\", \"company\": \"SkyNet Technologies\", \"position\": \"Junior Web Developer\", \"startDate\": \"2013-09-01\", \"endDate\": \"2015-05-31\", \"isCurrentlyWorking\": false, \"description\": \"Maintained legacy codebases and contributed to frontend redesigns for client portals.\", \"location\": \"Birmingham, UK\"}]", "skills": "[{\"id\": \"ec8d6010-3ab1-4a18-9358-7e69b205b23c\", \"name\": \"React.js\", \"category\": \"technical\", \"level\": \"advanced\"}, {\"id\": \"7c6b2b1d-37c1-4bce-bb8c-2d07fc845df0\", \"name\": \"Python\", \"category\": \"technical\", \"level\": \"expert\"}, {\"id\": \"e7b3fc96-f251-4b3e-9b65-6a8d4b8d85dc\", \"name\": \"Docker\", \"category\": \"technical\", \"level\": \"intermediate\"}, {\"id\": \"6c8c2be5-3b93-4d9e-b012-54e861529a29\", \"name\": \"Team Leadership\", \"category\": \"soft\", \"level\": \"expert\"}, {\"id\": \"3a84f5d2-68cb-4a20-92a1-fd1f3a5e4d0a\", \"name\": \"Problem Solving\", \"category\": \"soft\", \"level\": \"advanced\"}, {\"id\": \"d72f84c2-93ed-4e40-812e-2b1db155a7cf\", \"name\": \"Communication\", \"category\": \"soft\", \"level\": \"expert\"}, {\"id\": \"3fa0aa5f-ea04-4c32-928e-4e0b328b4382\", \"name\": \"French\", \"category\": \"language\", \"level\": \"intermediate\"}, {\"id\": \"5ad48e8b-98ef-4ed7-a3a5-88a1425418e1\", \"name\": \"English\", \"category\": \"language\", \"level\": \"native\"}, {\"id\": \"8c9d6934-644c-47f3-9854-c2a674f3cc29\", \"name\": \"Spanish\", \"category\": \"language\", \"level\": \"beginner\"}]", "references": "[{\"id\": \"85c7ed40-1c1d-41b7-8972-0d5121c9c80a\", \"name\": \"<PERSON>\", \"position\": \"Engineering Manager\", \"company\": \"TechNova Solutions\", \"email\": \"sarah.jen<PERSON>@technova.io\", \"phone\": \"+44 7900 111222\"}, {\"id\": \"1328bb14-e353-44ec-8e02-b9e3ec117548\", \"name\": \"Dr. <PERSON>\", \"position\": \"Professor\", \"company\": \"University of Oxford\", \"email\": \"<EMAIL>\", \"phone\": \"+44 1865 123456\"}, {\"id\": \"478b9f8d-d3e5-43ea-84e8-e793a13b46c5\", \"name\": \"<PERSON>\", \"position\": \"Team Lead\", \"company\": \"InnovateX\", \"email\": \"<EMAIL>\", \"phone\": \"+44 7755 234567\"}]", "cover_letter": "{\"recipientName\": \"Hiring Manager\", \"company\": \"NextGen Software Ltd.\", \"address\": \"45 Enterprise Way\", \"postalCode\": \"N1 1AA\", \"city\": \"London\", \"country\": \"United Kingdom\", \"email\": \"<EMAIL>\", \"phone\": \"+44 ************\", \"otherInformation\": \"Referred by internal employee <PERSON>.\", \"subject\": \"Application for Senior Developer Position\", \"date\": \"2025-07-10\", \"content\": \"Dear Hiring Manager,\\n\\nI am writing to express my interest in the Senior Developer position at NextGen Software Ltd. With over 10 years of experience in full-stack development and a strong foundation in React, Node.js, and AI-driven technologies, I am confident in my ability to contribute meaningfully to your team.\\n\\nI have attached my CV for your consideration. I would welcome the opportunity to discuss how I can help NextGen achieve its goals.\\n\\nThank you for your time and consideration.\\n\\nSincerely,\\nDaniel <PERSON>lu\", \"signatureFileId\": \"29a69f42-d9b0-4426-9ef7-f61a6fbe6f2e\"}", "created_at": "2025-07-10 16:18:25", "updated_at": "2025-07-10 16:23:37"}], "files": [], "timestamp": "2025-07-10T18:40:02.733273"}