# CV Template API Specification

## Overview
This document specifies the API endpoints required for the backend-generated CV template system.

## Base URL
```
{BACKEND_BASE_URL}/api/v1
```

## Authentication
All endpoints require JWT authentication via `Authorization: Bearer {access_token}` header.

---

## Endpoint 1: Get Available Templates

### `GET /templates`

**Description**: Retrieve list of available CV templates with their metadata.

**Request Headers**:
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**Response Format**:
```json
{
  "templates": [
    {
      "id": "german",
      "name": "German Template", 
      "description": "Professional German CV template following German standards",
      "language": "de",
      "preview_image": "https://backend.com/api/v1/templates/german/preview.png",
      "features": [
        "Multi-page layout",
        "Cover page with photo",
        "Table of contents", 
        "Certificate integration",
        "Cover letter support"
      ],
      "supported_sections": [
        "personal_info",
        "education", 
        "work_experience",
        "skills",
        "references",
        "cover_letter"
      ],
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    },
    {
      "id": "standard",
      "name": "Standard Template",
      "description": "Clean, professional template suitable for international use",
      "language": "en", 
      "preview_image": "https://backend.com/api/v1/templates/standard/preview.png",
      "features": [
        "Single-page layout",
        "Photo support",
        "Skills visualization"
      ],
      "supported_sections": [
        "personal_info",
        "education",
        "work_experience", 
        "skills",
        "references"
      ],
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 2
}
```

**HTTP Status Codes**:
- `200 OK`: Templates retrieved successfully
- `401 Unauthorized`: Invalid or missing authentication token
- `500 Internal Server Error`: Server error

---

## Endpoint 2: Get Template Details

### `GET /templates/{template_id}`

**Description**: Get detailed information about a specific template.

**Path Parameters**:
- `template_id` (string): Template identifier (e.g., "german", "standard")

**Request Headers**:
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**Response Format**:
```json
{
  "id": "german",
  "name": "German Template",
  "description": "Professional German CV template following German standards",
  "language": "de",
  "preview_image": "https://backend.com/api/v1/templates/german/preview.png",
  "features": [
    "Multi-page layout",
    "Cover page with photo", 
    "Table of contents",
    "Certificate integration",
    "Cover letter support"
  ],
  "supported_sections": [
    "personal_info",
    "education",
    "work_experience", 
    "skills",
    "references",
    "cover_letter"
  ],
  "configuration": {
    "page_size": "A4",
    "margins": "40pt",
    "font_family": "Helvetica",
    "color_scheme": {
      "primary": "#005A9C",
      "text": "#333333",
      "secondary": "#444444"
    },
    "supports_photo": true,
    "supports_certificates": true,
    "max_pages": null
  },
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

**HTTP Status Codes**:
- `200 OK`: Template details retrieved successfully
- `401 Unauthorized`: Invalid or missing authentication token
- `404 Not Found`: Template not found
- `500 Internal Server Error`: Server error

---

## Endpoint 3: Generate Template Preview

### `GET /templates/{template_id}/preview`

**Description**: Generate a preview image of the template with sample data.

**Path Parameters**:
- `template_id` (string): Template identifier

**Query Parameters**:
- `format` (optional, string): Image format ("png", "jpg"). Default: "png"
- `width` (optional, integer): Preview width in pixels. Default: 400
- `height` (optional, integer): Preview height in pixels. Default: 600

**Request Headers**:
```
Authorization: Bearer {access_token}
```

**Response**: Binary image data

**Response Headers**:
```
Content-Type: image/png (or image/jpeg)
Content-Length: {size_in_bytes}
Cache-Control: public, max-age=3600
```

**HTTP Status Codes**:
- `200 OK`: Preview generated successfully
- `401 Unauthorized`: Invalid or missing authentication token
- `404 Not Found`: Template not found
- `500 Internal Server Error`: Server error

---

## Updated CV Export Endpoint

### `GET /cv/{cv_id}/export`

**Description**: Export CV as PDF using backend-generated templates.

**Path Parameters**:
- `cv_id` (string): CV identifier

**Query Parameters**:
- `template_id` (optional, string): Override template (if not specified, uses CV's template field)
- `include_certificates` (optional, boolean): Include certificate attachments. Default: true
- `include_cover_letter` (optional, boolean): Include cover letter. Default: true

**Request Headers**:
```
Authorization: Bearer {access_token}
```

**Response**: Binary PDF data

**Response Headers**:
```
Content-Type: application/pdf
Content-Disposition: attachment; filename="{cv_title}_{template_id}.pdf"
Content-Length: {size_in_bytes}
```

**Enhanced Processing**:
1. Fetch CV data and associated files
2. Determine template (from query param or CV.template field)
3. Load template configuration and rendering engine
4. Apply language-specific translations if needed
5. Process and resize images (photo, certificates)
6. Generate PDF using template specifications
7. Merge certificate PDFs if applicable
8. Return final PDF document

**HTTP Status Codes**:
- `200 OK`: PDF generated successfully
- `401 Unauthorized`: Invalid or missing authentication token
- `404 Not Found`: CV or template not found
- `422 Unprocessable Entity`: Invalid template for CV data
- `500 Internal Server Error`: PDF generation failed

---

## Data Models

### Template Model
```python
class Template:
    id: str
    name: str
    description: str
    language: str
    preview_image: str
    features: List[str]
    supported_sections: List[str]
    configuration: dict
    created_at: datetime
    updated_at: datetime
```

### Template Configuration Schema
```json
{
  "page_size": "A4",
  "margins": "40pt",
  "font_family": "Helvetica", 
  "color_scheme": {
    "primary": "#005A9C",
    "text": "#333333",
    "secondary": "#444444"
  },
  "supports_photo": true,
  "supports_certificates": true,
  "max_pages": null,
  "sections": {
    "personal_info": {
      "required": true,
      "layout": "two_column"
    },
    "education": {
      "required": false,
      "layout": "chronological",
      "sort_order": "desc"
    }
  }
}
```

## Implementation Notes

### Template Storage
- Templates should be stored as configuration files or database records
- Template rendering logic should be modular and extensible
- Support for adding new templates without code changes

### Caching Strategy
- Cache template metadata for fast retrieval
- Cache preview images with appropriate TTL
- Cache font files and static assets

### Error Handling
- Graceful degradation for missing template features
- Detailed error messages for debugging
- Fallback to default template if specified template fails

### Security Considerations
- Validate template IDs to prevent path traversal
- Sanitize user data before PDF generation
- Rate limiting for PDF generation endpoints
- Secure handling of uploaded images and certificates

### Performance Optimization
- Async PDF generation for large documents
- Image optimization and caching
- Streaming responses for large PDFs
- Background processing for complex templates

This API specification provides a complete foundation for implementing backend-generated CV templates with proper template management, preview generation, and PDF export functionality.
