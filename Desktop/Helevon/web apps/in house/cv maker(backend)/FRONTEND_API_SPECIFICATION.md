# Frontend API Specification - CV Template Management System

## Overview

This document provides the API specification for the CV template management system endpoints. All endpoints require JWT authentication and return JSON responses unless otherwise specified.

## Base Configuration

- **Base URL**: `{BACKEND_BASE_URL}/api/v1`
- **Authentication**: JWT Bearer token required for all endpoints
- **Content-Type**: `application/json` (unless specified otherwise)

---

## 1. Get All Available Templates

### Endpoint Details
| Property | Value |
|----------|-------|
| **Route** | `/templates` |
| **HTTP Method** | `GET` |
| **Description** | Retrieve list of all available CV templates with metadata |

### Request Headers
| Header | Value | Required |
|--------|-------|----------|
| `Authorization` | `Bearer {access_token}` | ✅ |
| `Content-Type` | `application/json` | ✅ |

### Request Parameters
| Parameter | Type | Location | Required | Description |
|-----------|------|----------|----------|-------------|
| None | - | - | - | No parameters required |

### Response Format
```json
{
  "templates": [
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "language": "string",
      "preview_image": "string",
      "features": ["string"],
      "supported_sections": ["string"],
      "configuration": {
        "page_size": "string",
        "margins": "string",
        "font_family": "string",
        "color_scheme": {
          "primary": "string",
          "text": "string",
          "secondary": "string",
          "border": "string"
        },
        "supports_photo": "boolean",
        "supports_certificates": "boolean",
        "supports_cover_letter": "boolean",
        "max_pages": "number|null"
      },
      "created_at": "string (ISO 8601)",
      "updated_at": "string (ISO 8601)"
    }
  ],
  "total": "number"
}
```

### Status Codes
| Code | Description |
|------|-------------|
| `200` | Templates retrieved successfully |
| `401` | Invalid or missing authentication token |
| `500` | Internal server error |

---

## 2. Get Specific Template Details

### Endpoint Details
| Property | Value |
|----------|-------|
| **Route** | `/templates/{template_id}` |
| **HTTP Method** | `GET` |
| **Description** | Get detailed information about a specific template |

### Request Headers
| Header | Value | Required |
|--------|-------|----------|
| `Authorization` | `Bearer {access_token}` | ✅ |
| `Content-Type` | `application/json` | ✅ |

### Request Parameters
| Parameter | Type | Location | Required | Description |
|-----------|------|----------|----------|-------------|
| `template_id` | `string` | Path | ✅ | Template identifier (e.g., "german", "standard", "modern") |

### Response Format
```json
{
  "id": "string",
  "name": "string",
  "description": "string",
  "language": "string",
  "preview_image": "string",
  "features": ["string"],
  "supported_sections": ["string"],
  "configuration": {
    "page_size": "string",
    "margins": "string",
    "font_family": "string",
    "color_scheme": {
      "primary": "string",
      "text": "string",
      "secondary": "string",
      "border": "string"
    },
    "supports_photo": "boolean",
    "supports_certificates": "boolean",
    "supports_cover_letter": "boolean",
    "max_pages": "number|null",
    "sections": {
      "personal_info": {
        "required": "boolean",
        "layout": "string"
      },
      "education": {
        "required": "boolean",
        "layout": "string",
        "sort_order": "string"
      }
    }
  },
  "created_at": "string (ISO 8601)",
  "updated_at": "string (ISO 8601)"
}
```

### Status Codes
| Code | Description |
|------|-------------|
| `200` | Template details retrieved successfully |
| `401` | Invalid or missing authentication token |
| `404` | Template not found |
| `500` | Internal server error |

---

## 3. Generate Template Preview Image

### Endpoint Details
| Property | Value |
|----------|-------|
| **Route** | `/templates/{template_id}/preview` |
| **HTTP Method** | `GET` |
| **Description** | Generate a preview image of the template with sample data |

### Request Headers
| Header | Value | Required |
|--------|-------|----------|
| `Authorization` | `Bearer {access_token}` | ✅ |

### Request Parameters
| Parameter | Type | Location | Required | Description |
|-----------|------|----------|----------|-------------|
| `template_id` | `string` | Path | ✅ | Template identifier |
| `format` | `string` | Query | ❌ | Image format ("png", "jpg", "jpeg"). Default: "png" |
| `width` | `number` | Query | ❌ | Preview width in pixels (100-1200). Default: 400 |
| `height` | `number` | Query | ❌ | Preview height in pixels (150-1800). Default: 600 |
| `primary_color` | `string` | Query | ❌ | Primary color override (hex format: #RRGGBB) |

### Response Format
- **Content-Type**: `image/png` or `image/jpeg`
- **Body**: Binary image data

### Response Headers
| Header | Value |
|--------|-------|
| `Content-Type` | `image/png` or `image/jpeg` |
| `Content-Length` | `{size_in_bytes}` |
| `Cache-Control` | `public, max-age=3600` |

### Status Codes
| Code | Description |
|------|-------------|
| `200` | Preview generated successfully |
| `401` | Invalid or missing authentication token |
| `404` | Template not found |
| `500` | Preview generation failed |

---

## 4. Enhanced CV Export

### Endpoint Details
| Property | Value |
|----------|-------|
| **Route** | `/cv/{cv_id}/export` |
| **HTTP Method** | `GET` |
| **Description** | Export CV as PDF with enhanced template and customization options |

### Request Headers
| Header | Value | Required |
|--------|-------|----------|
| `Authorization` | `Bearer {access_token}` | ✅ |

### Request Parameters
| Parameter | Type | Location | Required | Description |
|-----------|------|----------|----------|-------------|
| `cv_id` | `string` | Path | ✅ | CV unique identifier |
| `template_id` | `string` | Query | ❌ | Template ID override (overrides CV's template field) |
| `include_certificates` | `boolean` | Query | ❌ | Include certificate attachments. Default: true |
| `include_cover_letter` | `boolean` | Query | ❌ | Include cover letter. Default: true |
| `primary_color` | `string` | Query | ❌ | Primary color override (hex format: #RRGGBB) |
| `format` | `string` | Query | ❌ | Export format. Default: "pdf" |

### Response Format
- **Content-Type**: `application/pdf`
- **Body**: Binary PDF data

### Response Headers
| Header | Value |
|--------|-------|
| `Content-Type` | `application/pdf` |
| `Content-Disposition` | `attachment; filename="{cv_title}_{template_id}.pdf"` |
| `Content-Length` | `{size_in_bytes}` |

### Status Codes
| Code | Description |
|------|-------------|
| `200` | PDF generated successfully |
| `401` | Invalid or missing authentication token |
| `404` | CV or template not found |
| `422` | Invalid template for CV data or validation error |
| `500` | PDF generation failed |
| `503` | PDF export service unavailable |

---

## Available Templates

| Template ID | Name | Language | Features |
|-------------|------|----------|----------|
| `german` | German Template | `de` | Multi-page, cover page, table of contents, certificates, cover letter, German translations |
| `standard` | Standard Template | `en` | Clean design, photo support, international use |
| `modern` | Modern Template | `en` | Contemporary design, progress bars, two-column layout |

---

## Error Response Format

All endpoints return errors in the following format:

```json
{
  "detail": "string"
}
```

For validation errors (422), additional error details may be included:

```json
{
  "detail": "string",
  "errors": [
    {
      "field": "string",
      "message": "string"
    }
  ]
}
```

---

## Usage Examples

### Get All Templates
```
GET /api/v1/templates
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Get German Template Details
```
GET /api/v1/templates/german
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Generate Template Preview with Custom Color
```
GET /api/v1/templates/german/preview?width=500&height=700&primary_color=%23FF5722
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Export CV with German Template and Custom Color
```
GET /api/v1/cv/123e4567-e89b-12d3-a456-426614174000/export?template_id=german&primary_color=%23FF5722&include_certificates=true
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

---

---

## 5. Upload Profile Photo

### Endpoint Details
| Property | Value |
|----------|-------|
| **Route** | `/cv/{cv_id}/photo` |
| **HTTP Method** | `POST` |
| **Description** | Upload profile photo for a CV (replaces existing photo) |

### Request Headers
| Header | Value | Required |
|--------|-------|----------|
| `Authorization` | `Bearer {access_token}` | ✅ |
| `Content-Type` | `multipart/form-data` | ✅ |

### Request Parameters
| Parameter | Type | Location | Required | Description |
|-----------|------|----------|----------|-------------|
| `cv_id` | `string` | Path | ✅ | CV unique identifier |
| `file` | `File` | Form Data | ✅ | Photo file (JPG, PNG only, max 5MB, min 50x50px) |

### Response Format
```json
{
  "id": "string",
  "name": "string",
  "type": "string",
  "size": "number",
  "category": "photo",
  "url": "/api/v1/files/{file_id}",
  "created_at": "string (ISO 8601)",
  "updated_at": "string (ISO 8601)"
}
```

### Status Codes
| Code | Description |
|------|-------------|
| `201` | Photo uploaded successfully |
| `400` | Invalid file format, size, or validation error |
| `401` | Invalid or missing authentication token |
| `404` | CV not found |
| `500` | Upload failed |

---

## 6. Upload Certificate

### Endpoint Details
| Property | Value |
|----------|-------|
| **Route** | `/cv/{cv_id}/education/{education_id}/certificate` |
| **HTTP Method** | `POST` |
| **Description** | Upload certificate for a specific education entry |

### Request Headers
| Header | Value | Required |
|--------|-------|----------|
| `Authorization` | `Bearer {access_token}` | ✅ |
| `Content-Type` | `multipart/form-data` | ✅ |

### Request Parameters
| Parameter | Type | Location | Required | Description |
|-----------|------|----------|----------|-------------|
| `cv_id` | `string` | Path | ✅ | CV unique identifier |
| `education_id` | `string` | Path | ✅ | Education entry identifier |
| `file` | `File` | Form Data | ✅ | Certificate file (PDF, JPG, PNG only, max 5MB) |

### Response Format
```json
{
  "id": "string",
  "name": "string",
  "type": "string",
  "size": "number",
  "category": "certificate",
  "url": "/api/v1/files/{file_id}",
  "created_at": "string (ISO 8601)",
  "updated_at": "string (ISO 8601)"
}
```

### Status Codes
| Code | Description |
|------|-------------|
| `201` | Certificate uploaded successfully |
| `400` | Invalid file format, size, or validation error |
| `401` | Invalid or missing authentication token |
| `404` | CV or education entry not found |
| `500` | Upload failed |

---

## 7. Get File Content

### Endpoint Details
| Property | Value |
|----------|-------|
| **Route** | `/files/{file_id}` |
| **HTTP Method** | `GET` |
| **Description** | Retrieve file content (photo or certificate) |

### Request Headers
| Header | Value | Required |
|--------|-------|----------|
| `Authorization` | `Bearer {access_token}` | ✅ |

### Request Parameters
| Parameter | Type | Location | Required | Description |
|-----------|------|----------|----------|-------------|
| `file_id` | `string` | Path | ✅ | File unique identifier |

### Response Format
- **Content-Type**: Original file MIME type (`image/jpeg`, `image/png`, `application/pdf`)
- **Body**: Binary file data

### Response Headers
| Header | Value |
|--------|-------|
| `Content-Type` | Original file MIME type |
| `Content-Disposition` | `inline; filename="{original_filename}"` |
| `Content-Length` | `{size_in_bytes}` |
| `Cache-Control` | `private, max-age=3600` |

### Status Codes
| Code | Description |
|------|-------------|
| `200` | File retrieved successfully |
| `401` | Invalid or missing authentication token |
| `404` | File not found |
| `500` | File retrieval failed |

---

## Notes for Frontend Developers

1. **Authentication**: All endpoints require a valid JWT access token in the Authorization header
2. **Color Format**: Primary color parameters must be in hex format (#RRGGBB)
3. **Template IDs**: Use exact template IDs as returned by the templates list endpoint
4. **Binary Responses**: Preview and export endpoints return binary data (images/PDFs)
5. **Caching**: Preview images are cached for 1 hour (3600 seconds)
6. **File Downloads**: Export endpoint sets appropriate headers for file downloads
7. **File Uploads**: Use `FormData` with `multipart/form-data` for file uploads
8. **File Display**: Use `/files/{file_id}` endpoint to display uploaded photos/certificates
9. **File Validation**: Photos must be JPG/PNG (min 50x50px), certificates can be PDF/JPG/PNG
10. **File Storage**: Files are stored in database as base64, linked to CV via file IDs
11. **Error Handling**: Always check status codes and handle error responses appropriately
