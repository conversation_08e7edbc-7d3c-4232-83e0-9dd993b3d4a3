2025-07-10 14:31:39 | ERROR    | app.core.dependencies:get_current_user_token:68 | Token validation error: 
2025-07-10 14:31:39 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 14:56:48 | ERROR    | app.core.database:get_db:108 | Database session error: [{'type': 'missing', 'loc': ('body', 'file'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}, {'type': 'missing', 'loc': ('body', 'category'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-07-10 14:57:50 | ERROR    | app.core.database:get_db:108 | Database session error: [{'type': 'model_attributes_type', 'loc': ('body',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'input': [{'institution': 'Olorunfunmi Senior Grammar School, Nigeria', 'degree': 'Abitur', 'field': 'Abitur', 'startDate': '2001-01-01', 'endDate': '2009-01-20', 'current': False, 'location': 'Lagos, Nigeria', 'description': '', 'certificateUrl': '', 'id': 'cf14d29d-0044-4b75-9e04-dc07a12bde3e'}], 'url': 'https://errors.pydantic.dev/2.5/v/model_attributes_type'}]
2025-07-10 14:58:14 | ERROR    | app.core.database:get_db:108 | Database session error: [{'type': 'model_attributes_type', 'loc': ('body',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'input': [{'institution': 'Olorunfunmi Senior Grammar School, Nigeria', 'degree': 'Abitur', 'field': 'Abitur', 'startDate': '2001-01-01', 'endDate': '2009-01-20', 'current': False, 'location': 'Lagos, Nigeria', 'description': '', 'certificateUrl': '', 'id': 'ab714ba5-7032-457a-adf2-5680027fd20f'}], 'url': 'https://errors.pydantic.dev/2.5/v/model_attributes_type'}]
2025-07-10 15:13:31 | ERROR    | app.core.dependencies:get_current_user_token:68 | Token validation error: 
2025-07-10 15:13:31 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 15:13:32 | ERROR    | app.core.dependencies:get_current_user_token:68 | Token validation error: 
2025-07-10 15:13:32 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 15:30:32 | ERROR    | app.core.database:get_db:108 | Database session error: [{'type': 'missing', 'loc': ('body', 'skills', 0, 'id'), 'msg': 'Field required', 'input': {'name': 'English', 'category': 'language', 'level': 'expert'}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-07-10 16:08:54 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:09:13 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:10:31 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:10:56 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:11:58 | ERROR    | app.endpoints.auth:login_user:185 | Login error: (sqlite3.OperationalError) no such table: users
[SQL: SELECT users.id, users.name, users.email, users.email_verified, users.password, users.image, users.language, users.created_at, users.updated_at, users.last_login_at, users.login_attempts, users.locked_until, users.refresh_token_hash, users.refresh_token_expires_at 
FROM users 
WHERE users.email = ?]
[parameters: ('<EMAIL>',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 16:11:58 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:13:06 | ERROR    | app.endpoints.cv:get_cv:191 | Error retrieving CV 6f897884-020a-46e4-8d01-1c9ab5ac3981: 1 validation error for CVWithFiles
cover_letter
  Input should be a valid dictionary [type=dict_type, input_value='{"recipientName": "Frau ...signatureFileId": null}', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/dict_type
2025-07-10 16:13:06 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:13:34 | ERROR    | app.endpoints.cv:get_cv:191 | Error retrieving CV 6f897884-020a-46e4-8d01-1c9ab5ac3981: 1 validation error for CVWithFiles
cover_letter
  Input should be a valid dictionary [type=dict_type, input_value='{"recipientName": "Frau ...signatureFileId": null}', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/dict_type
2025-07-10 16:13:34 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:14:16 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:14:41 | ERROR    | app.core.database:get_db:108 | Database session error: [{'type': 'string_type', 'loc': ('body', 'user_id'), 'msg': 'Input should be a valid string', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}]
2025-07-10 16:14:52 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:16:24 | ERROR    | app.services.pdf_optimization_service:generate_with_monitoring:176 | PDF generation failed after 0.01s: name 'TA_JUSTIFY' is not defined
2025-07-10 16:16:24 | ERROR    | app.services.pdf_service:generate_cv_pdf:124 | PDF generation failed for CV 6f897884-020a-46e4-8d01-1c9ab5ac3981: name 'TA_JUSTIFY' is not defined
2025-07-10 16:16:24 | ERROR    | app.endpoints.cv:export_cv_pdf:879 | PDF export error: name 'TA_JUSTIFY' is not defined
2025-07-10 16:16:24 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:17:14 | ERROR    | app.services.pdf_optimization_service:generate_with_monitoring:176 | PDF generation failed after 0.00s: name 'TA_JUSTIFY' is not defined
2025-07-10 16:17:14 | ERROR    | app.services.pdf_service:generate_cv_pdf:124 | PDF generation failed for CV 6f897884-020a-46e4-8d01-1c9ab5ac3981: name 'TA_JUSTIFY' is not defined
2025-07-10 16:17:14 | ERROR    | app.endpoints.cv:export_cv_pdf:879 | PDF export error: name 'TA_JUSTIFY' is not defined
2025-07-10 16:17:14 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:18:19 | ERROR    | app.endpoints.files:upload_profile_photo:156 | Profile photo upload failed: name 'and_' is not defined
2025-07-10 16:18:19 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:20:11 | ERROR    | app.endpoints.files:upload_certificate:289 | Certificate upload failed: 1 validation error for FileResponse
user_id
  Field required [type=missing, input_value={'id': 'cac6ea9f-2ae4-4c3...025, 7, 10, 14, 20, 11)}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.5/v/missing
2025-07-10 16:20:11 | ERROR    | app.core.database:get_db:108 | Database session error: 
