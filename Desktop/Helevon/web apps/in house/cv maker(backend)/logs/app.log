2025-07-08 13:45:32 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:48:45 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:48:46 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:48:46 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:48:46 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:48:46 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:48:47 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:48:47 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:48:47 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:49:30 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.37ms - IP: 127.0.0.1
2025-07-08 13:49:49 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 1.36ms - IP: 127.0.0.1
2025-07-08 13:50:07 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 13:50:07 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 523.83ms - IP: 127.0.0.1
2025-07-08 13:50:38 | WARNING  | app.endpoints.auth:register_user:55 | Registration attempt with existing email: <EMAIL>
2025-07-08 13:50:38 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 13:50:38 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Email already registered - Status: 400
2025-07-08 13:50:38 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 400 - 19.67ms - IP: 127.0.0.1
2025-07-08 13:51:28 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:51:28 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:51:29 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:51:29 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:51:29 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:51:29 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:51:29 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:51:29 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:51:59 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:00 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:00 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:52:00 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:52:00 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:52:00 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:52:00 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:52:00 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:52:00 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.14ms - IP: 127.0.0.1
2025-07-08 13:52:00 | WARNING  | app.endpoints.auth:register_user:55 | Registration attempt with existing email: <EMAIL>
2025-07-08 13:52:00 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 13:52:00 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Email already registered - Status: 400
2025-07-08 13:52:00 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 400 - 45.59ms - IP: 127.0.0.1
2025-07-08 13:52:19 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:19 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:20 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:52:20 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:52:20 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:52:20 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:52:20 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:52:20 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:52:33 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:33 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:33 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:52:33 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:52:33 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:52:33 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:52:33 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:52:33 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:52:49 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:50 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:50 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:52:50 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:52:50 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:52:50 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:52:50 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:52:50 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:53:08 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:53:08 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:53:08 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:53:08 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:53:08 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:53:08 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:53:08 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:53:08 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:53:23 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:53:23 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:53:23 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:53:23 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:53:23 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:53:23 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:53:23 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:53:23 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:53:34 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.26ms - IP: 127.0.0.1
2025-07-08 13:53:35 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 13:53:35 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 425.58ms - IP: 127.0.0.1
2025-07-08 13:53:35 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 13:53:35 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 423.35ms - IP: 127.0.0.1
2025-07-08 13:53:35 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 14.01ms - User: ********-b91f-4253-bf1a-4b05eafc91fc - IP: 127.0.0.1
2025-07-08 13:53:35 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 13:53:35 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Cannot create CV for another user - Status: 403
2025-07-08 13:53:35 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 403 - 15.42ms - User: ********-b91f-4253-bf1a-4b05eafc91fc - IP: 127.0.0.1
2025-07-08 13:54:37 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.24ms - IP: 127.0.0.1
2025-07-08 13:54:37 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 13:54:37 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 365.88ms - IP: 127.0.0.1
2025-07-08 13:54:38 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 13:54:38 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 620.79ms - IP: 127.0.0.1
2025-07-08 13:54:38 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 12.37ms - User: f1a1a5a6-226f-49ca-8777-a999dedd4b64 - IP: 127.0.0.1
2025-07-08 13:54:38 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 13:54:38 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Cannot create CV for another user - Status: 403
2025-07-08 13:54:38 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 403 - 20.54ms - User: f1a1a5a6-226f-49ca-8777-a999dedd4b64 - IP: 127.0.0.1
2025-07-08 13:56:03 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.29ms - IP: 127.0.0.1
2025-07-08 13:56:03 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 13:56:03 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 505.20ms - IP: 127.0.0.1
2025-07-08 13:56:03 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 13:56:03 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 439.02ms - IP: 127.0.0.1
2025-07-08 13:56:03 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 15.61ms - User: 3c492222-3962-4357-8beb-58900582f53f - IP: 127.0.0.1
2025-07-08 13:56:04 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 13:56:04 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Cannot create CV for another user - Status: 403
2025-07-08 13:56:04 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 403 - 15.39ms - User: 3c492222-3962-4357-8beb-58900582f53f - IP: 127.0.0.1
2025-07-08 13:58:08 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 3.19ms - IP: 127.0.0.1
2025-07-08 13:58:09 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 99.47ms - IP: 127.0.0.1
2025-07-08 13:59:39 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:59:40 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:59:40 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:59:40 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:59:40 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:59:40 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:59:40 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:59:40 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:59:56 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:59:56 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:59:56 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:59:56 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:59:56 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:59:56 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:59:56 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:59:56 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 14:00:09 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.06ms - IP: 127.0.0.1
2025-07-08 14:00:09 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 14:00:09 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 431.50ms - IP: 127.0.0.1
2025-07-08 14:00:10 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 14:00:10 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 337.17ms - IP: 127.0.0.1
2025-07-08 14:00:10 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 8.64ms - User: 199be5c7-b1de-46ec-8d98-b673d7ef7392 - IP: 127.0.0.1
2025-07-08 14:00:10 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 19.10ms - User: 199be5c7-b1de-46ec-8d98-b673d7ef7392 - IP: 127.0.0.1
2025-07-08 14:00:10 | INFO     | app.endpoints.cv:create_cv:89 | CV created: 063d7ed2-5c0d-47c0-b27b-579d42c32d41 <NAME_EMAIL>
2025-07-08 14:00:10 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 201 - 46.09ms - User: 199be5c7-b1de-46ec-8d98-b673d7ef7392 - IP: 127.0.0.1
2025-07-08 14:00:34 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 14:00:34 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 14:00:34 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 14:00:34 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 14:00:34 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 14:00:34 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 14:00:34 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 14:00:34 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 14:00:51 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 14:00:51 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 14:00:51 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 14:00:51 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 14:00:51 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 14:00:51 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 14:00:51 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 14:00:51 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 14:01:02 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.82ms - IP: 127.0.0.1
2025-07-08 14:01:02 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 14:01:02 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 449.03ms - IP: 127.0.0.1
2025-07-08 14:01:03 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 14:01:03 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 418.53ms - IP: 127.0.0.1
2025-07-08 14:01:03 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 16.89ms - User: 3ac93f06-18c7-41a4-953a-00f904a77331 - IP: 127.0.0.1
2025-07-08 14:01:03 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 22.96ms - User: 3ac93f06-18c7-41a4-953a-00f904a77331 - IP: 127.0.0.1
2025-07-08 14:01:03 | INFO     | app.endpoints.cv:create_cv:89 | CV created: e2e61a61-a691-4952-a7a1-36928a902c7c <NAME_EMAIL>
2025-07-08 14:01:03 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 201 - 48.73ms - User: 3ac93f06-18c7-41a4-953a-00f904a77331 - IP: 127.0.0.1
2025-07-08 14:01:04 | ERROR    | app.endpoints.cv:export_cv_pdf:817 | PDF export error: name 'settings' is not defined
2025-07-08 14:01:04 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 14:01:04 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Failed to export PDF - Status: 500
2025-07-08 14:01:04 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e2e61a61-a691-4952-a7a1-36928a902c7c/export - 500 - 513.35ms - User: 3ac93f06-18c7-41a4-953a-00f904a77331 - IP: 127.0.0.1
2025-07-08 14:02:49 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 14:02:49 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 14:02:49 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 14:02:49 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 14:02:49 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 14:02:49 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 14:02:49 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 14:02:49 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 14:03:14 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.62ms - IP: 127.0.0.1
2025-07-08 14:03:15 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 14:03:15 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 432.96ms - IP: 127.0.0.1
2025-07-08 14:03:15 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 14:03:15 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 479.74ms - IP: 127.0.0.1
2025-07-08 14:03:15 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 18.85ms - User: 4548d5d2-a111-420e-9ac8-bf9eac6bb55b - IP: 127.0.0.1
2025-07-08 14:03:15 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 9.07ms - User: 4548d5d2-a111-420e-9ac8-bf9eac6bb55b - IP: 127.0.0.1
2025-07-08 14:03:15 | INFO     | app.endpoints.cv:create_cv:90 | CV created: 0f22329e-7522-48b9-b39a-05186ce32a2c <NAME_EMAIL>
2025-07-08 14:03:15 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 201 - 52.28ms - User: 4548d5d2-a111-420e-9ac8-bf9eac6bb55b - IP: 127.0.0.1
2025-07-08 14:03:15 | INFO     | app.services.pdf_service:generate_cv_pdf:136 | PDF generated for CV 0f22329e-7522-48b9-b39a-05186ce32a2c, template: standard
2025-07-08 14:03:15 | INFO     | app.endpoints.cv:export_cv_pdf:803 | PDF exported for CV 0f22329e-7522-48b9-b39a-05186ce32a2c <NAME_EMAIL>
2025-07-08 14:03:15 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/0f22329e-7522-48b9-b39a-05186ce32a2c/export - 200 - 208.60ms - User: 4548d5d2-a111-420e-9ac8-bf9eac6bb55b - IP: 127.0.0.1
2025-07-08 14:05:21 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 385.23ms - IP: 127.0.0.1
2025-07-08 14:05:24 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 1067.46ms - IP: 127.0.0.1
2025-07-08 14:05:24 | WARNING  | app.core.logging:log_performance_issue:228 | Slow Operation: GET /openapi.json - 1067.46ms (threshold: 1000.0ms) - Details: {'user_id': None, 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (X11; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0'}
2025-07-08 15:22:25 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 15:22:25 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 15:22:25 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 15:22:25 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 15:22:25 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 15:22:25 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 15:22:25 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 15:22:25 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 17:02:21 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 200 - 231.47ms - IP: 127.0.0.1
2025-07-08 17:02:21 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 45.77ms - IP: 127.0.0.1
2025-07-08 17:03:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 95.81ms - IP: 127.0.0.1
2025-07-08 17:08:54 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 218.13ms - IP: 127.0.0.1
2025-07-08 17:09:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 24.07ms - IP: 127.0.0.1
2025-07-08 17:09:54 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.43ms - IP: 127.0.0.1
2025-07-08 17:10:29 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/auth/signin - 200 - 109.70ms - IP: 127.0.0.1
2025-07-08 17:10:30 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 17:10:30 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 654.43ms - IP: 127.0.0.1
2025-07-08 17:10:30 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/user/account - 200 - 11.79ms - IP: 127.0.0.1
2025-07-08 17:10:30 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 63.59ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:10:30 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 200 - 1.21ms - IP: 127.0.0.1
2025-07-08 17:10:30 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 30.26ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:10:30 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.59ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:10:30 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/user/profile - 200 - 1.40ms - IP: 127.0.0.1
2025-07-08 17:10:30 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/profile - 200 - 64.69ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:10:33 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv - 200 - 2.28ms - IP: 127.0.0.1
2025-07-08 17:10:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 24.73ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:10:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 63.06ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:10:33 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.42ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:10:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 88.85ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:10:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 19.00ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:10:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/profile - 200 - 19.43ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:10:33 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 7.07ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:10:34 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/profile - 200 - 11.96ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:10:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.27ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:10:54 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.19ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:11:07 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 8.66ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:11:08 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 64.70ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:11:08 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 17.95ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:11:08 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.83ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:11:08 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/profile - 200 - 9.10ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:11:09 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/profile - 200 - 48.11ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:11:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.88ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:11:54 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.09ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:12:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.13ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:12:54 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.15ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:13:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.20ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:13:54 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.18ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:14:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.92ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:14:54 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.79ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:15:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.86ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:15:55 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 9.85ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:16:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.61ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:16:55 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.54ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:17:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.75ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:17:55 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.19ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:18:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.25ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:18:55 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.28ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:19:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.81ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:19:55 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 18.89ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:20:38 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 200 - 10.93ms - IP: 127.0.0.1
2025-07-08 17:20:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 9.16ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:20:55 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.06ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:21:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.54ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:21:56 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.72ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:22:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 66.96ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:22:56 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.66ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:23:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 59.29ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:23:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.21ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:24:39 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.03ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:24:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.37ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:25:39 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.12ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:25:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.05ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:26:39 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.11ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:26:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.14ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:27:39 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.36ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:27:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 62.18ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:28:39 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 7.06ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:28:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.12ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:29:39 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.51ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:29:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.93ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:30:39 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 200 - 1.74ms - IP: 127.0.0.1
2025-07-08 17:30:39 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.12ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:30:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.78ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:31:39 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.45ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:31:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.18ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:32:39 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.33ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:32:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.01ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:33:39 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.75ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:33:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.44ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:34:39 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.14ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:34:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.66ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:35:39 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.18ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:35:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 11.89ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:36:39 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.55ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:36:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.69ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:37:39 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.58ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:37:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.22ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:38:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.77ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:38:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 66.29ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:39:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.35ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:39:59 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 771.08ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 127.0.0.1
2025-07-08 17:40:40 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 200 - 4.33ms - IP: 127.0.0.1
2025-07-08 17:40:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:40:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.86ms - IP: 127.0.0.1
2025-07-08 17:40:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:40:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 9.92ms - IP: 127.0.0.1
2025-07-08 17:41:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:41:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 15.53ms - IP: 127.0.0.1
2025-07-08 17:41:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:41:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.19ms - IP: 127.0.0.1
2025-07-08 17:42:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:42:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.03ms - IP: 127.0.0.1
2025-07-08 17:42:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:42:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 8.04ms - IP: 127.0.0.1
2025-07-08 17:43:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:43:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.34ms - IP: 127.0.0.1
2025-07-08 17:43:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:43:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 8.94ms - IP: 127.0.0.1
2025-07-08 17:44:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:44:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.21ms - IP: 127.0.0.1
2025-07-08 17:44:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:44:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 10.56ms - IP: 127.0.0.1
2025-07-08 17:45:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:45:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.09ms - IP: 127.0.0.1
2025-07-08 17:45:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:45:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 9.44ms - IP: 127.0.0.1
2025-07-08 17:46:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:46:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.88ms - IP: 127.0.0.1
2025-07-08 17:46:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:46:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 21.20ms - IP: 127.0.0.1
2025-07-08 17:47:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:47:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 7.35ms - IP: 127.0.0.1
2025-07-08 17:47:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:47:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 9.06ms - IP: 127.0.0.1
2025-07-08 17:48:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:48:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.18ms - IP: 127.0.0.1
2025-07-08 17:48:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:48:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.89ms - IP: 127.0.0.1
2025-07-08 17:49:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:49:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.15ms - IP: 127.0.0.1
2025-07-08 17:49:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:49:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 7.60ms - IP: 127.0.0.1
2025-07-08 17:50:40 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 200 - 3.18ms - IP: 127.0.0.1
2025-07-08 17:50:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:50:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.41ms - IP: 127.0.0.1
2025-07-08 17:50:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:50:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 8.90ms - IP: 127.0.0.1
2025-07-08 17:51:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:51:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.30ms - IP: 127.0.0.1
2025-07-08 17:51:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:51:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.91ms - IP: 127.0.0.1
2025-07-08 17:52:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:52:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 9.41ms - IP: 127.0.0.1
2025-07-08 17:52:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:52:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.30ms - IP: 127.0.0.1
2025-07-08 17:53:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:53:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.57ms - IP: 127.0.0.1
2025-07-08 17:53:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:53:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.82ms - IP: 127.0.0.1
2025-07-08 17:54:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:54:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 7.45ms - IP: 127.0.0.1
2025-07-08 17:54:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:54:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.03ms - IP: 127.0.0.1
2025-07-08 17:55:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:55:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.31ms - IP: 127.0.0.1
2025-07-08 17:55:58 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-08 17:55:58 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.95ms - IP: 127.0.0.1
