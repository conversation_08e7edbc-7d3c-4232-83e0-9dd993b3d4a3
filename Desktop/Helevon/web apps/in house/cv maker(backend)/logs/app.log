2025-07-10 14:28:54 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 14:28:54 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 14:28:55 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 14:28:55 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 14:28:55 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 14:28:55 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 14:28:55 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 14:28:55 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 14:31:38 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 200 - 7.67ms - IP: 12*******
2025-07-10 14:31:38 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 14:31:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 20.20ms - IP: 12*******
2025-07-10 14:31:38 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 14:31:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 7.42ms - IP: 12*******
2025-07-10 14:31:39 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/user/account - 200 - 1.51ms - IP: 12*******
2025-07-10 14:31:39 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 14:31:39 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 14:31:39 | WARNING  | app.core.dependencies:get_current_user_token:54 | Invalid or expired access token
2025-07-10 14:31:39 | ERROR    | app.core.dependencies:get_current_user_token:68 | Token validation error: 
2025-07-10 14:31:39 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 14:31:39 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Could not validate credentials - Status: 401
2025-07-10 14:31:39 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 401 - 138.39ms - IP: 12*******
2025-07-10 14:31:39 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/auth/refresh-token - 200 - 10.05ms - IP: 12*******
2025-07-10 14:31:41 | INFO     | app.endpoints.auth:refresh_access_token:224 | Token refreshed for user: <EMAIL>
2025-07-10 14:31:41 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/refresh-token - 200 - 759.42ms - IP: 12*******
2025-07-10 14:31:41 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 142.68ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:31:42 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 18.83ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:31:42 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv - 200 - 14.44ms - IP: 12*******
2025-07-10 14:31:42 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 27.07ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:31:42 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.52ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:31:42 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 23.76ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:31:42 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 18.63ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:31:42 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 37.60ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:31:42 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 38.99ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:31:42 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 51.48ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:31:49 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 11.88ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:31:49 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 32.45ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:31:49 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.00ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:31:49 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 14.64ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:31:50 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 51.89ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:31:50 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 13.60ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:32:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.75ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:33:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 19.01ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:34:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.24ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:35:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.57ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:36:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 44.36ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:37:20 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 166.33ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:37:21 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 31.88ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:37:21 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 106.92ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:37:38 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.13ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:38:39 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 8.30ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:38:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 4.17ms - IP: 12*******
2025-07-10 14:38:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 21.02ms - IP: 12*******
2025-07-10 14:38:51 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.18ms - IP: 12*******
2025-07-10 14:38:54 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 6.63ms - IP: 12*******
2025-07-10 14:38:55 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 400 - 5.38ms - IP: 12*******
2025-07-10 14:38:55 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 400 - 77.29ms - IP: 12*******
2025-07-10 14:38:57 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 400 - 39.97ms - IP: 12*******
2025-07-10 14:38:57 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 400 - 22.75ms - IP: 12*******
2025-07-10 14:38:59 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 400 - 1.05ms - IP: 12*******
2025-07-10 14:38:59 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 400 - 16.68ms - IP: 12*******
2025-07-10 14:39:02 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 400 - 0.98ms - IP: 12*******
2025-07-10 14:39:03 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 400 - 4.03ms - IP: 12*******
2025-07-10 14:39:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.87ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:39:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.35ms - IP: 12*******
2025-07-10 14:39:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 0.97ms - IP: 12*******
2025-07-10 14:39:51 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.83ms - IP: 12*******
2025-07-10 14:39:54 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 5.89ms - IP: 12*******
2025-07-10 14:40:40 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.80ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:40:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 0.95ms - IP: 12*******
2025-07-10 14:40:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.33ms - IP: 12*******
2025-07-10 14:40:51 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.00ms - IP: 12*******
2025-07-10 14:40:54 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.29ms - IP: 12*******
2025-07-10 14:41:41 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 200 - 1.02ms - IP: 12*******
2025-07-10 14:41:41 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.15ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:41:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 6.28ms - IP: 12*******
2025-07-10 14:41:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 17.70ms - IP: 12*******
2025-07-10 14:41:51 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 0.94ms - IP: 12*******
2025-07-10 14:41:54 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.26ms - IP: 12*******
2025-07-10 14:42:42 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.83ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:42:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.37ms - IP: 12*******
2025-07-10 14:42:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.53ms - IP: 12*******
2025-07-10 14:42:51 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.42ms - IP: 12*******
2025-07-10 14:42:54 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 0.96ms - IP: 12*******
2025-07-10 14:43:43 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.90ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:43:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 0.99ms - IP: 12*******
2025-07-10 14:43:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 6.46ms - IP: 12*******
2025-07-10 14:43:51 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 0.95ms - IP: 12*******
2025-07-10 14:43:55 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 311.28ms - IP: 12*******
2025-07-10 14:44:44 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.07ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:44:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.09ms - IP: 12*******
2025-07-10 14:44:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 2.06ms - IP: 12*******
2025-07-10 14:44:51 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.13ms - IP: 12*******
2025-07-10 14:44:54 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 0.94ms - IP: 12*******
2025-07-10 14:45:45 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.09ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:45:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.15ms - IP: 12*******
2025-07-10 14:45:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 2.14ms - IP: 12*******
2025-07-10 14:45:51 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.01ms - IP: 12*******
2025-07-10 14:45:54 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.44ms - IP: 12*******
2025-07-10 14:46:46 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.40ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:46:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.21ms - IP: 12*******
2025-07-10 14:46:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 0.97ms - IP: 12*******
2025-07-10 14:46:51 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.03ms - IP: 12*******
2025-07-10 14:46:54 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.20ms - IP: 12*******
2025-07-10 14:47:46 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.41ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:47:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.71ms - IP: 12*******
2025-07-10 14:47:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 0.89ms - IP: 12*******
2025-07-10 14:47:51 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 0.99ms - IP: 12*******
2025-07-10 14:47:54 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.18ms - IP: 12*******
2025-07-10 14:48:47 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.94ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:48:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.22ms - IP: 12*******
2025-07-10 14:48:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 0.91ms - IP: 12*******
2025-07-10 14:48:51 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.26ms - IP: 12*******
2025-07-10 14:48:54 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 0.92ms - IP: 12*******
2025-07-10 14:49:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.40ms - IP: 12*******
2025-07-10 14:49:48 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.65ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:49:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 11.62ms - IP: 12*******
2025-07-10 14:49:51 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 0.93ms - IP: 12*******
2025-07-10 14:49:54 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 4.44ms - IP: 12*******
2025-07-10 14:50:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 0.96ms - IP: 12*******
2025-07-10 14:50:49 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 13.34ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:50:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.05ms - IP: 12*******
2025-07-10 14:50:51 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.31ms - IP: 12*******
2025-07-10 14:50:54 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.00ms - IP: 12*******
2025-07-10 14:51:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.28ms - IP: 12*******
2025-07-10 14:51:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 3.02ms - IP: 12*******
2025-07-10 14:51:50 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 200 - 1.81ms - IP: 12*******
2025-07-10 14:51:50 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.86ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:51:51 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 19.52ms - IP: 12*******
2025-07-10 14:51:54 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 0.91ms - IP: 12*******
2025-07-10 14:52:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 2.31ms - IP: 12*******
2025-07-10 14:52:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.14ms - IP: 12*******
2025-07-10 14:52:50 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.47ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:52:51 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 4.23ms - IP: 12*******
2025-07-10 14:52:54 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.21ms - IP: 12*******
2025-07-10 14:53:03 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 6.84ms - IP: 12*******
2025-07-10 14:53:04 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.35ms - IP: 12*******
2025-07-10 14:53:06 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.66ms - IP: 12*******
2025-07-10 14:53:09 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 400 - 1.36ms - IP: 12*******
2025-07-10 14:53:51 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.99ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:18 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.70ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:18 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.63ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:19 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/user/account - 200 - 7.91ms - IP: 12*******
2025-07-10 14:54:19 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 200 - 100.39ms - IP: 12*******
2025-07-10 14:54:19 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 215.83ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:19 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 8.05ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:19 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 19.50ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:19 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 200 - 421.33ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:19 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 19.50ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:19 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 86.19ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:19 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 200 - 48.17ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:19 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 39.39ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:20 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 16.35ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:24 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 17.88ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:24 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 32.29ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:24 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv - 200 - 6.99ms - IP: 12*******
2025-07-10 14:54:24 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 10.58ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:24 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 60.51ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:24 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 15.34ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:24 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 25.39ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:24 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 76.16ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:25 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 35.06ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:27 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 16.01ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:27 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 35.63ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:27 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.36ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:27 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 22.42ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:27 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 9.18ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:27 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 18.80ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:31 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 21.91ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:31 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 36.60ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:31 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 38.42ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:31 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 10.12ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:31 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 64.53ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:31 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.68ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:31 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 14.76ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:31 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 18.47ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:32 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 8.36ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:32 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 26.98ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:32 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 8.64ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:32 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.22ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 46.69ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 35.33ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:44 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 9.36ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:44 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 9.24ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:44 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 19.56ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:44 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 37.90ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:44 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv/e9300bef-59ea-4ef1-a87c-9b5b3399bf1b - 200 - 16.60ms - IP: 12*******
2025-07-10 14:54:44 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 22.16ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:44 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 35.81ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:44 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 16.84ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:44 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e9300bef-59ea-4ef1-a87c-9b5b3399bf1b - 200 - 70.95ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:44 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 30.73ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:44 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e9300bef-59ea-4ef1-a87c-9b5b3399bf1b - 200 - 44.59ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:44 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 31.68ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:47 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 22.35ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:47 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 95.07ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:47 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 61.06ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:47 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 35.25ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:47 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 126.69ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:47 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 255.03ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:47 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 132.98ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:47 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 13.48ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:48 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 72.40ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:48 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 25.70ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:49 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.65ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:49 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.07ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:49 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 19.13ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:54:49 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 23.38ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:17 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.82ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:17 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.29ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:17 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 20.28ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:18 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 200 - 42.68ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:18 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 95.02ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:18 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 43.95ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:18 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 200 - 167.47ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:18 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 89.58ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:18 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 16.28ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:18 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 31.66ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:18 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 14.11ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:30 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 15.27ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:30 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 33.07ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:30 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 55.48ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:30 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 87.29ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:30 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 9.80ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:31 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 19.76ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:31 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 24.00ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:31 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 14.54ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 11.48ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 17.73ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:33 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.52ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:33 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.33ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 61.82ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:55:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 45.22ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:17 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.98ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:48 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv/e9300bef-59ea-4ef1-a87c-9b5b3399bf1b/upload - 200 - 1.44ms - IP: 12*******
2025-07-10 14:56:48 | ERROR    | app.core.database:get_db:108 | Database session error: [{'type': 'missing', 'loc': ('body', 'file'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}, {'type': 'missing', 'loc': ('body', 'category'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-07-10 14:56:48 | WARNING  | app.core.exceptions:validation_exception_handler:165 | Validation error: [{'type': 'missing', 'loc': ('body', 'file'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}, {'type': 'missing', 'loc': ('body', 'category'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-07-10 14:56:48 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv/e9300bef-59ea-4ef1-a87c-9b5b3399bf1b/upload - 422 - 48.45ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:52 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.60ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:52 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.88ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:53 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 32.83ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:53 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e9300bef-59ea-4ef1-a87c-9b5b3399bf1b - 200 - 87.78ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:53 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 31.41ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:53 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e9300bef-59ea-4ef1-a87c-9b5b3399bf1b - 200 - 36.10ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:53 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.65ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:53 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 20.64ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:53 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 10.49ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:53 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 11.14ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:53 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 15.53ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:57 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 46.54ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:57 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 73.22ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:57 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 73.04ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 28.25ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:57 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 161.33ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 7.90ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:57 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 58.08ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:56:57 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 14.39ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:57:03 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 17.96ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:57:03 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 28.40ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:57:03 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 10.42ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:57:03 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 25.41ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:57:03 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 11.90ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:57:04 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 11.15ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:57:50 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv/e9300bef-59ea-4ef1-a87c-9b5b3399bf1b/education - 200 - 2.86ms - IP: 12*******
2025-07-10 14:57:50 | ERROR    | app.core.database:get_db:108 | Database session error: [{'type': 'model_attributes_type', 'loc': ('body',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'input': [{'institution': 'Olorunfunmi Senior Grammar School, Nigeria', 'degree': 'Abitur', 'field': 'Abitur', 'startDate': '2001-01-01', 'endDate': '2009-01-20', 'current': False, 'location': 'Lagos, Nigeria', 'description': '', 'certificateUrl': '', 'id': 'cf14d29d-0044-4b75-9e04-dc07a12bde3e'}], 'url': 'https://errors.pydantic.dev/2.5/v/model_attributes_type'}]
2025-07-10 14:57:50 | WARNING  | app.core.exceptions:validation_exception_handler:165 | Validation error: [{'type': 'model_attributes_type', 'loc': ('body',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'input': [{'institution': 'Olorunfunmi Senior Grammar School, Nigeria', 'degree': 'Abitur', 'field': 'Abitur', 'startDate': '2001-01-01', 'endDate': '2009-01-20', 'current': False, 'location': 'Lagos, Nigeria', 'description': '', 'certificateUrl': '', 'id': 'cf14d29d-0044-4b75-9e04-dc07a12bde3e'}], 'url': 'https://errors.pydantic.dev/2.5/v/model_attributes_type'}]
2025-07-10 14:57:50 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/e9300bef-59ea-4ef1-a87c-9b5b3399bf1b/education - 422 - 16.21ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:57:52 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.15ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:58:02 | INFO     | app.core.logging:log_api_access:179 | GET / - 200 - 2.13ms - IP: 12*******
2025-07-10 14:58:03 | INFO     | app.core.logging:log_api_access:179 | GET /favicon.ico - 404 - 5.04ms - IP: 12*******
2025-07-10 14:58:14 | ERROR    | app.core.database:get_db:108 | Database session error: [{'type': 'model_attributes_type', 'loc': ('body',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'input': [{'institution': 'Olorunfunmi Senior Grammar School, Nigeria', 'degree': 'Abitur', 'field': 'Abitur', 'startDate': '2001-01-01', 'endDate': '2009-01-20', 'current': False, 'location': 'Lagos, Nigeria', 'description': '', 'certificateUrl': '', 'id': 'ab714ba5-7032-457a-adf2-5680027fd20f'}], 'url': 'https://errors.pydantic.dev/2.5/v/model_attributes_type'}]
2025-07-10 14:58:14 | WARNING  | app.core.exceptions:validation_exception_handler:165 | Validation error: [{'type': 'model_attributes_type', 'loc': ('body',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'input': [{'institution': 'Olorunfunmi Senior Grammar School, Nigeria', 'degree': 'Abitur', 'field': 'Abitur', 'startDate': '2001-01-01', 'endDate': '2009-01-20', 'current': False, 'location': 'Lagos, Nigeria', 'description': '', 'certificateUrl': '', 'id': 'ab714ba5-7032-457a-adf2-5680027fd20f'}], 'url': 'https://errors.pydantic.dev/2.5/v/model_attributes_type'}]
2025-07-10 14:58:14 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/e9300bef-59ea-4ef1-a87c-9b5b3399bf1b/education - 422 - 16.49ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:58:53 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.94ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 14:58:58 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 3.36ms - IP: 12*******
2025-07-10 14:58:58 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 98.56ms - IP: 12*******
2025-07-10 14:59:08 | INFO     | app.core.logging:log_api_access:179 | GET /redoc - 200 - 3.34ms - IP: 12*******
2025-07-10 14:59:08 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 2.67ms - IP: 12*******
2025-07-10 14:59:54 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.01ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:00:54 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.09ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:01:54 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 200 - 2.68ms - IP: 12*******
2025-07-10 15:01:54 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:01:54 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 7.57ms - IP: 12*******
2025-07-10 15:02:55 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:02:55 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.30ms - IP: 12*******
2025-07-10 15:03:55 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:03:55 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.12ms - IP: 12*******
2025-07-10 15:04:55 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:04:55 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.30ms - IP: 12*******
2025-07-10 15:05:55 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:05:55 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.27ms - IP: 12*******
2025-07-10 15:06:56 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:06:56 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 26.29ms - IP: 12*******
2025-07-10 15:07:55 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:07:55 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.11ms - IP: 12*******
2025-07-10 15:08:55 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:08:56 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 61.06ms - IP: 12*******
2025-07-10 15:09:55 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:09:56 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 67.80ms - IP: 12*******
2025-07-10 15:10:55 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:10:55 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.29ms - IP: 12*******
2025-07-10 15:11:56 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 200 - 4.48ms - IP: 12*******
2025-07-10 15:11:56 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:11:56 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 7.02ms - IP: 12*******
2025-07-10 15:12:56 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:12:56 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 7.74ms - IP: 12*******
2025-07-10 15:13:31 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv - 200 - 5.62ms - IP: 12*******
2025-07-10 15:13:31 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:13:31 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:13:31 | WARNING  | app.core.dependencies:get_current_user_token:54 | Invalid or expired access token
2025-07-10 15:13:31 | ERROR    | app.core.dependencies:get_current_user_token:68 | Token validation error: 
2025-07-10 15:13:31 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 15:13:31 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Could not validate credentials - Status: 401
2025-07-10 15:13:31 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 401 - 226.23ms - IP: 12*******
2025-07-10 15:13:32 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:13:32 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 15:13:32 | WARNING  | app.core.dependencies:get_current_user_token:54 | Invalid or expired access token
2025-07-10 15:13:32 | ERROR    | app.core.dependencies:get_current_user_token:68 | Token validation error: 
2025-07-10 15:13:32 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 15:13:32 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Could not validate credentials - Status: 401
2025-07-10 15:13:32 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 401 - 66.35ms - IP: 12*******
2025-07-10 15:13:32 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/auth/refresh-token - 200 - 21.68ms - IP: 12*******
2025-07-10 15:13:32 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/auth/refresh-token - 200 - 74.65ms - IP: 12*******
2025-07-10 15:13:33 | INFO     | app.endpoints.auth:refresh_access_token:224 | Token refreshed for user: <EMAIL>
2025-07-10 15:13:33 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/refresh-token - 200 - 593.17ms - IP: 12*******
2025-07-10 15:13:33 | INFO     | app.endpoints.auth:refresh_access_token:224 | Token refreshed for user: <EMAIL>
2025-07-10 15:13:33 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/refresh-token - 200 - 610.94ms - IP: 12*******
2025-07-10 15:13:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 169.75ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:13:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 349.14ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:13:36 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 8.16ms - IP: 12*******
2025-07-10 15:13:56 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 13.34ms - IP: 12*******
2025-07-10 15:14:22 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:14:23 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:14:23 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 15:14:23 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 15:14:23 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 15:14:23 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 15:14:23 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 15:14:23 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 15:14:31 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/auth/signin - 200 - 5.09ms - IP: 12*******
2025-07-10 15:14:32 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-10 15:14:32 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 578.32ms - IP: 12*******
2025-07-10 15:14:32 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/user/account - 200 - 2.85ms - IP: 12*******
2025-07-10 15:14:32 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 31.02ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:32 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 13.67ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:32 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.36ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:32 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 17.30ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 29.68ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 76.63ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 48.56ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:33 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 11.91ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 64.41ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 33.55ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:33 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 26.37ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:34 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 28.32ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:36 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 15.17ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:36 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 6.65ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:36 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.81ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:36 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 15.35ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:36 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 28.08ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:36 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 61.60ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:14:56 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.54ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:15:27 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:15:27 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:15:27 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 15:15:27 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 15:15:27 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 15:15:27 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 15:15:27 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 15:15:27 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 15:15:54 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:15:54 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:15:54 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 15:15:54 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 15:15:54 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 15:15:54 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 15:15:54 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 15:15:54 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 15:15:56 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 9.16ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:16:03 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d/education - 200 - 3.47ms - IP: 12*******
2025-07-10 15:16:03 | INFO     | app.endpoints.cv:update_education:465 | Education updated for CV 3f0931a0-07e1-4bd2-8e01-c04b04e7499d <NAME_EMAIL>
2025-07-10 15:16:03 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d/education - 200 - 154.44ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:16:03 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 200 - 1.34ms - IP: 12*******
2025-07-10 15:16:03 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 200 - 58.24ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:16:08 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:16:08 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:16:08 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 15:16:08 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 15:16:08 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 15:16:08 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 15:16:08 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 15:16:08 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 15:16:25 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:16:25 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:16:25 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 15:16:25 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 15:16:25 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 15:16:25 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 15:16:25 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 15:16:25 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 15:16:44 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:16:45 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:16:45 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 15:16:45 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 15:16:45 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 15:16:45 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 15:16:45 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 15:16:45 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 15:16:56 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.33ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:17:41 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d/work-experience - 200 - 1.36ms - IP: 12*******
2025-07-10 15:17:42 | INFO     | app.endpoints.cv:update_work_experience:528 | Work experience updated for CV 3f0931a0-07e1-4bd2-8e01-c04b04e7499d <NAME_EMAIL>
2025-07-10 15:17:42 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d/work-experience - 200 - 118.37ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:17:42 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 200 - 30.64ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:17:56 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.86ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:18:47 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:18:47 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:18:48 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 15:18:48 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 15:18:48 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 15:18:48 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 15:18:48 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 15:18:48 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 15:18:56 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 12.42ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:19:35 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 68.81ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:19:35 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 13.55ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:19:36 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.77ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:19:36 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.34ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:19:36 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 12.78ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:19:36 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 13.55ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:19:56 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.82ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:21:13 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:21:13 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 15:21:13 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 15:21:13 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 15:21:13 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 15:21:13 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 15:21:13 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 15:21:13 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 15:21:38 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 3.67ms - IP: 12*******
2025-07-10 15:21:39 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 128.06ms - IP: 12*******
2025-07-10 15:21:57 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 200 - 0.99ms - IP: 12*******
2025-07-10 15:21:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 13.10ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:22:43 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-10 15:22:43 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 556.89ms - IP: 12*******
2025-07-10 15:22:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.16ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:23:23 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 28.48ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:23:39 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 200 - 23.24ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:23:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.06ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:24:05 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e9300bef-59ea-4ef1-a87c-9b5b3399bf1b - 200 - 19.44ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:24:39 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d - 200 - 8.80ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:24:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.02ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:25:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.58ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:26:55 | INFO     | app.endpoints.cv:update_personal_info:402 | Personal info updated for CV 3f0931a0-07e1-4bd2-8e01-c04b04e7499d <NAME_EMAIL>
2025-07-10 15:26:55 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d/personal-info - 200 - 97.86ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:26:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.25ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:27:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 8.34ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:28:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.06ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:29:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.77ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:30:32 | ERROR    | app.core.database:get_db:108 | Database session error: [{'type': 'missing', 'loc': ('body', 'skills', 0, 'id'), 'msg': 'Field required', 'input': {'name': 'English', 'category': 'language', 'level': 'expert'}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-07-10 15:30:32 | WARNING  | app.core.exceptions:validation_exception_handler:165 | Validation error: [{'type': 'missing', 'loc': ('body', 'skills', 0, 'id'), 'msg': 'Field required', 'input': {'name': 'English', 'category': 'language', 'level': 'expert'}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-07-10 15:30:32 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d/skills - 422 - 21.26ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:30:55 | INFO     | app.endpoints.cv:update_skills:591 | Skills updated for CV 3f0931a0-07e1-4bd2-8e01-c04b04e7499d <NAME_EMAIL>
2025-07-10 15:30:55 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/3f0931a0-07e1-4bd2-8e01-c04b04e7499d/skills - 200 - 98.99ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:30:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.42ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:31:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/user/account - 200 - 1.46ms - IP: 12*******
2025-07-10 15:31:49 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /api/v1/cv - 200 - 2.38ms - IP: 12*******
2025-07-10 15:31:49 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 34.15ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:31:49 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 39.23ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:31:49 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 18.95ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:31:49 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 17.45ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:31:49 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.02ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:31:49 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.44ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:31:49 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 9.43ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:31:49 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 5.92ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:31:51 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 9.82ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:31:51 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 9.08ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:31:51 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.65ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:31:51 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.29ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:31:52 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 22.93ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:31:52 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 35.15ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:31:57 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 200 - 2.71ms - IP: 12*******
2025-07-10 15:31:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.29ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:32:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.27ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:33:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.36ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:34:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.57ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:35:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.30ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:36:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.79ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:37:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.17ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:38:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.31ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:39:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.87ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:40:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.05ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:41:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.50ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 15:42:57 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 200 - 2.45ms - IP: 12*******
2025-07-10 15:42:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.78ms - User: 29e54786-730f-46c1-af02-0521839dceff - IP: 12*******
2025-07-10 16:06:33 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:06:34 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:06:34 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:06:34 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:06:34 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:06:34 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:06:34 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:06:34 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:06:57 | INFO     | app.core.logging:log_api_access:179 | OPTIONS /health - 200 - 2.52ms - IP: 12*******
2025-07-10 16:06:57 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 16:06:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 9.02ms - IP: 12*******
2025-07-10 16:07:57 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 16:07:57 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 6.15ms - IP: 12*******
2025-07-10 16:07:59 | INFO     | app.core.logging:log_api_access:179 | GET /redoc - 200 - 1.11ms - IP: 12*******
2025-07-10 16:07:59 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 95.13ms - IP: 12*******
2025-07-10 16:08:09 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 2.81ms - IP: 12*******
2025-07-10 16:08:09 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 5.54ms - IP: 12*******
2025-07-10 16:08:54 | WARNING  | app.endpoints.auth:login_user:144 | Failed login attempt for: <EMAIL>
2025-07-10 16:08:54 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:08:54 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Invalid email or password - Status: 401
2025-07-10 16:08:54 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 401 - 538.74ms - IP: 12*******
2025-07-10 16:09:13 | WARNING  | app.endpoints.auth:login_user:144 | Failed login attempt for: <EMAIL>
2025-07-10 16:09:13 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:09:13 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Invalid email or password - Status: 401
2025-07-10 16:09:13 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 401 - 395.74ms - IP: 12*******
2025-07-10 16:10:25 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:10:25 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:10:25 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:10:25 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:10:25 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:10:26 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:10:26 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:10:26 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:10:31 | WARNING  | app.endpoints.auth:login_user:124 | Login attempt with non-existent email: <EMAIL>
2025-07-10 16:10:31 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:10:31 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Invalid email or password - Status: 401
2025-07-10 16:10:31 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 401 - 51.99ms - IP: 12*******
2025-07-10 16:10:42 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:10:42 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:10:42 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:10:42 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:10:42 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:10:42 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:10:42 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:10:42 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:10:48 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 1.53ms - IP: 12*******
2025-07-10 16:10:48 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 76.82ms - IP: 12*******
2025-07-10 16:10:56 | WARNING  | app.endpoints.auth:login_user:124 | Login attempt with non-existent email: <EMAIL>
2025-07-10 16:10:56 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:10:56 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Invalid email or password - Status: 401
2025-07-10 16:10:56 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 401 - 49.90ms - IP: 12*******
2025-07-10 16:11:52 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:11:52 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:11:52 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:11:52 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:11:52 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:11:52 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:11:52 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:11:52 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:11:58 | ERROR    | app.endpoints.auth:login_user:185 | Login error: (sqlite3.OperationalError) no such table: users
[SQL: SELECT users.id, users.name, users.email, users.email_verified, users.password, users.image, users.language, users.created_at, users.updated_at, users.last_login_at, users.login_attempts, users.locked_until, users.refresh_token_hash, users.refresh_token_expires_at 
FROM users 
WHERE users.email = ?]
[parameters: ('<EMAIL>',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-10 16:11:58 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:11:58 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Login failed - Status: 500
2025-07-10 16:11:58 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 500 - 71.14ms - IP: 12*******
2025-07-10 16:12:14 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:12:14 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:12:15 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:12:15 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:12:15 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:12:15 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:12:15 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:12:15 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:12:17 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-10 16:12:17 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 500.59ms - IP: 12*******
2025-07-10 16:12:46 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 24.71ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:13:06 | ERROR    | app.endpoints.cv:get_cv:191 | Error retrieving CV 6f897884-020a-46e4-8d01-1c9ab5ac3981: 1 validation error for CVWithFiles
cover_letter
  Input should be a valid dictionary [type=dict_type, input_value='{"recipientName": "Frau ...signatureFileId": null}', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/dict_type
2025-07-10 16:13:06 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:13:06 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Failed to retrieve CV - Status: 500
2025-07-10 16:13:06 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/6f897884-020a-46e4-8d01-1c9ab5ac3981 - 500 - 38.13ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:13:34 | ERROR    | app.endpoints.cv:get_cv:191 | Error retrieving CV 6f897884-020a-46e4-8d01-1c9ab5ac3981: 1 validation error for CVWithFiles
cover_letter
  Input should be a valid dictionary [type=dict_type, input_value='{"recipientName": "Frau ...signatureFileId": null}', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/dict_type
2025-07-10 16:13:34 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:13:34 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Failed to retrieve CV - Status: 500
2025-07-10 16:13:34 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/6f897884-020a-46e4-8d01-1c9ab5ac3981 - 500 - 12.76ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:14:16 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:14:16 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Cannot create CV for another user - Status: 403
2025-07-10 16:14:16 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 403 - 10.76ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:14:29 | WARNING  | app.core.exceptions:validation_exception_handler:165 | Validation error: [{'type': 'json_invalid', 'loc': ('body', 70), 'msg': 'JSON decode error', 'input': {}, 'ctx': {'error': 'Expecting property name enclosed in double quotes'}}]
2025-07-10 16:14:29 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 422 - 3.35ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:14:41 | ERROR    | app.core.database:get_db:108 | Database session error: [{'type': 'string_type', 'loc': ('body', 'user_id'), 'msg': 'Input should be a valid string', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}]
2025-07-10 16:14:41 | WARNING  | app.core.exceptions:validation_exception_handler:165 | Validation error: [{'type': 'string_type', 'loc': ('body', 'user_id'), 'msg': 'Input should be a valid string', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}]
2025-07-10 16:14:41 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 422 - 73.54ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:14:52 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:14:52 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Cannot create CV for another user - Status: 403
2025-07-10 16:14:52 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 403 - 12.78ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:16:24 | ERROR    | app.services.pdf_optimization_service:generate_with_monitoring:176 | PDF generation failed after 0.01s: name 'TA_JUSTIFY' is not defined
2025-07-10 16:16:24 | ERROR    | app.services.pdf_service:generate_cv_pdf:124 | PDF generation failed for CV 6f897884-020a-46e4-8d01-1c9ab5ac3981: name 'TA_JUSTIFY' is not defined
2025-07-10 16:16:24 | ERROR    | app.endpoints.cv:export_cv_pdf:879 | PDF export error: name 'TA_JUSTIFY' is not defined
2025-07-10 16:16:24 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:16:24 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Failed to export PDF - Status: 500
2025-07-10 16:16:24 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/6f897884-020a-46e4-8d01-1c9ab5ac3981/export - 500 - 364.34ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:17:14 | ERROR    | app.services.pdf_optimization_service:generate_with_monitoring:176 | PDF generation failed after 0.00s: name 'TA_JUSTIFY' is not defined
2025-07-10 16:17:14 | ERROR    | app.services.pdf_service:generate_cv_pdf:124 | PDF generation failed for CV 6f897884-020a-46e4-8d01-1c9ab5ac3981: name 'TA_JUSTIFY' is not defined
2025-07-10 16:17:14 | ERROR    | app.endpoints.cv:export_cv_pdf:879 | PDF export error: name 'TA_JUSTIFY' is not defined
2025-07-10 16:17:14 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:17:14 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Failed to export PDF - Status: 500
2025-07-10 16:17:14 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/6f897884-020a-46e4-8d01-1c9ab5ac3981/export - 500 - 22.45ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:18:19 | INFO     | app.services.file_validation_service:validate_file:121 | File validation passed: 8f969e48-380c-43ad-a946-377976cbde59.jpg
2025-07-10 16:18:19 | ERROR    | app.endpoints.files:upload_profile_photo:156 | Profile photo upload failed: name 'and_' is not defined
2025-07-10 16:18:19 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:18:19 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Failed to upload profile photo - Status: 500
2025-07-10 16:18:19 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv/6f897884-020a-46e4-8d01-1c9ab5ac3981/photo - 500 - 204.53ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:20:11 | INFO     | app.services.file_validation_service:validate_file:121 | File validation passed: 20250611_174935_Widerspruchsbescheid.pdf
2025-07-10 16:20:11 | INFO     | app.endpoints.files:upload_certificate:273 | Certificate uploaded for education edu_001: 20250611_174935_Widerspruchsbescheid.pdf
2025-07-10 16:20:11 | ERROR    | app.endpoints.files:upload_certificate:289 | Certificate upload failed: 1 validation error for FileResponse
user_id
  Field required [type=missing, input_value={'id': 'cac6ea9f-2ae4-4c3...025, 7, 10, 14, 20, 11)}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.5/v/missing
2025-07-10 16:20:11 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 16:20:11 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Failed to upload certificate - Status: 500
2025-07-10 16:20:11 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv/6f897884-020a-46e4-8d01-1c9ab5ac3981/education/edu_001/certificate - 500 - 137.52ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:20:58 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/templates - 200 - 7.48ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:21:29 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/templates - 200 - 7.72ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:21:44 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/templates/german - 200 - 10.30ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:22:10 | INFO     | app.services.template_service:generate_preview:304 | Preview generated for template german
2025-07-10 16:22:10 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/templates/german/preview - 200 - 73.13ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:23:30 | INFO     | app.services.template_service:generate_preview:304 | Preview generated for template standard
2025-07-10 16:23:30 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/templates/standard/preview - 200 - 23.61ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:24:44 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:24:44 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:24:45 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:24:45 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:24:45 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:24:45 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:24:45 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:24:45 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:25:13 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:25:13 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:25:13 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:25:13 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:25:13 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:25:13 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:25:13 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:25:13 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:26:23 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:26:24 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:26:24 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:26:24 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:26:24 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:26:24 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:26:24 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:26:24 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:26:56 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:26:57 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:26:57 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:26:57 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:26:57 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:26:57 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:26:57 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:26:57 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:27:35 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:27:36 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:27:36 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:27:36 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:27:36 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:27:36 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:27:36 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:27:36 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:28:11 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:28:11 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:28:12 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:28:12 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:28:12 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:28:12 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:28:12 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:28:12 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:28:40 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:28:40 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:28:41 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:28:41 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:28:41 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:28:41 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:28:41 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:28:41 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:29:43 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:29:43 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:29:43 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:29:43 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:29:43 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:29:43 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:29:43 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:29:43 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:31:21 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 3.84ms - IP: 12*******
2025-07-10 16:31:22 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 77.22ms - IP: 12*******
2025-07-10 16:31:38 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 125.24ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:32:11 | INFO     | app.endpoints.cv:create_cv:83 | CV created: e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 16:32:11 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 201 - 91.97ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:32:24 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/6f897884-020a-46e4-8d01-1c9ab5ac3981 - 200 - 22.61ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:32:27 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 7.38ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:32:57 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/6f897884-020a-46e4-8d01-1c9ab5ac3981 - 200 - 17.82ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:37:56 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:37:56 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:37:56 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:37:56 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:37:56 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:37:56 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:37:56 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:37:56 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:38:59 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:39:00 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:39:00 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:39:00 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:39:00 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:39:00 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:39:00 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:39:00 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:39:17 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:39:17 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:39:17 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:39:17 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:39:17 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:39:17 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:39:17 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:39:17 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:39:42 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:39:42 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:39:43 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:39:43 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:39:43 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:39:43 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:39:43 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:39:43 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:40:18 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:40:19 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:40:19 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:40:19 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:40:19 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:40:19 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:40:19 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:40:19 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:40:52 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:40:52 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:40:52 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:40:52 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:40:52 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:40:52 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:40:52 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:40:52 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:42:24 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:42:24 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:42:25 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:42:25 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:42:25 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:42:25 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:42:25 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:42:25 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:44:00 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 2.88ms - IP: 12*******
2025-07-10 16:44:00 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 69.54ms - IP: 12*******
2025-07-10 16:44:23 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-10 16:44:23 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 505.07ms - IP: 12*******
2025-07-10 16:44:44 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 15.34ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:45:00 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f - 200 - 24.68ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:47:49 | INFO     | app.core.logging:log_api_access:179 | GET /redoc - 200 - 1.42ms - IP: 12*******
2025-07-10 16:47:50 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 3.73ms - IP: 12*******
2025-07-10 16:50:11 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV 6f897884-020a-46e4-8d01-1c9ab5ac3981, template: german
2025-07-10 16:50:11 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.04s
2025-07-10 16:50:11 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV 6f897884-020a-46e4-8d01-1c9ab5ac3981 <NAME_EMAIL>
2025-07-10 16:50:11 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/6f897884-020a-46e4-8d01-1c9ab5ac3981/export - 200 - 317.66ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:53:03 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV 6f897884-020a-46e4-8d01-1c9ab5ac3981, template: standard
2025-07-10 16:53:03 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.03s
2025-07-10 16:53:03 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV 6f897884-020a-46e4-8d01-1c9ab5ac3981 <NAME_EMAIL>
2025-07-10 16:53:03 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/6f897884-020a-46e4-8d01-1c9ab5ac3981/export - 200 - 90.56ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:54:09 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/templates - 200 - 10.54ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:54:33 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV 6f897884-020a-46e4-8d01-1c9ab5ac3981, template: modern
2025-07-10 16:54:33 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.03s
2025-07-10 16:54:33 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV 6f897884-020a-46e4-8d01-1c9ab5ac3981 <NAME_EMAIL>
2025-07-10 16:54:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/6f897884-020a-46e4-8d01-1c9ab5ac3981/export - 200 - 73.91ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:59:04 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:59:05 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:59:06 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:59:06 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:59:06 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:59:06 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:59:06 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:59:06 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:59:12 | INFO     | app.endpoints.cv:update_personal_info:406 | Personal info updated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 16:59:12 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/personal-info - 200 - 116.12ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 16:59:18 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:59:18 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:59:18 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:59:18 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:59:18 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:59:18 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:59:18 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:59:18 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:59:33 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:59:33 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 16:59:33 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 16:59:33 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 16:59:33 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 16:59:33 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 16:59:33 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 16:59:33 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 16:59:46 | INFO     | app.endpoints.cv:update_education:482 | Education updated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 16:59:46 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/education - 200 - 337.01ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:00:10 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:00:10 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:00:11 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:00:11 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:00:11 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:00:11 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:00:11 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:00:11 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:00:31 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:00:31 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:00:31 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:00:31 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:00:31 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:00:32 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:00:32 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:00:32 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:00:53 | INFO     | app.endpoints.cv:update_work_experience:558 | Work experience updated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 17:00:53 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/work-experience - 200 - 179.21ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:01:06 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:01:06 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:01:07 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:01:07 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:01:07 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:01:07 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:01:07 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:01:07 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:01:30 | INFO     | app.endpoints.cv:update_skills:634 | Skills updated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 17:01:30 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/skills - 200 - 201.35ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:01:48 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:01:49 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:01:49 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:01:49 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:01:49 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:01:49 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:01:49 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:01:49 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:02:00 | INFO     | app.endpoints.cv:update_references:710 | References updated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 17:02:00 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/references - 200 - 442.78ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:02:19 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:02:19 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:02:19 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:02:19 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:02:19 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:02:19 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:02:19 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:02:19 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:02:38 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 17:02:38 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: CV not found - Status: 404
2025-07-10 17:02:38 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40/cover-letter - 404 - 128.25ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:02:46 | INFO     | app.endpoints.cv:update_cover_letter:791 | Cover letter updated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 17:02:46 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/cover-letter - 200 - 159.26ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:03:05 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f, template: german
2025-07-10 17:03:05 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.10s
2025-07-10 17:03:05 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 17:03:05 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/export - 200 - 303.49ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:08:03 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f, template: standard
2025-07-10 17:08:03 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.02s
2025-07-10 17:08:03 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 17:08:03 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/export - 200 - 72.11ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:08:27 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f, template: modern
2025-07-10 17:08:27 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.03s
2025-07-10 17:08:27 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 17:08:27 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/export - 200 - 71.64ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:10:47 | INFO     | app.services.file_validation_service:validate_file:121 | File validation passed: 10632488.jpg
2025-07-10 17:10:47 | INFO     | app.endpoints.files:upload_profile_photo:140 | Profile photo uploaded for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f: 10632488.jpg
2025-07-10 17:10:47 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/photo - 201 - 101.26ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:11:01 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f, template: german
2025-07-10 17:11:01 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.03s
2025-07-10 17:11:01 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 17:11:01 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/export - 200 - 119.36ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:13:40 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f, template: german
2025-07-10 17:13:40 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.04s
2025-07-10 17:13:40 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 17:13:40 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/export - 200 - 153.38ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:14:17 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f, template: german
2025-07-10 17:14:17 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.07s
2025-07-10 17:14:17 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 17:14:17 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/export - 200 - 190.62ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:15:33 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 17:15:33 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 17:15:33 | WARNING  | app.core.dependencies:get_current_user_token:54 | Invalid or expired access token
2025-07-10 17:15:33 | ERROR    | app.core.dependencies:get_current_user_token:68 | Token validation error: 
2025-07-10 17:15:33 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 17:15:33 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Could not validate credentials - Status: 401
2025-07-10 17:15:33 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/export - 401 - 75.25ms - IP: 12*******
2025-07-10 17:15:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 17:15:40 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 17:15:40 | WARNING  | app.core.dependencies:get_current_user_token:54 | Invalid or expired access token
2025-07-10 17:15:40 | ERROR    | app.core.dependencies:get_current_user_token:68 | Token validation error: 
2025-07-10 17:15:40 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 17:15:40 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Could not validate credentials - Status: 401
2025-07-10 17:15:40 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/export - 401 - 8.16ms - IP: 12*******
2025-07-10 17:16:10 | INFO     | app.endpoints.auth:refresh_access_token:224 | Token refreshed for user: <EMAIL>
2025-07-10 17:16:10 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/refresh-token - 200 - 113.33ms - IP: 12*******
2025-07-10 17:16:32 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f, template: standard
2025-07-10 17:16:32 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.50s
2025-07-10 17:16:32 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 17:16:32 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/export - 200 - 586.87ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:17:22 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f, template: german
2025-07-10 17:17:22 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.02s
2025-07-10 17:17:22 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 17:17:22 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/export - 200 - 125.85ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:17:37 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f, template: modern
2025-07-10 17:17:37 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.02s
2025-07-10 17:17:37 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 17:17:37 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/export - 200 - 89.76ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:18:48 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f - 200 - 64.31ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:19:20 | INFO     | app.services.file_validation_service:validate_file:121 | File validation passed: 20250611_174935_Widerspruchsbescheid.pdf
2025-07-10 17:19:21 | INFO     | app.endpoints.files:upload_certificate:275 | Certificate uploaded for education 3c4e8a36-1b3e-4f21-bf5f-9e3f97c0d120: 20250611_174935_Widerspruchsbescheid.pdf
2025-07-10 17:19:21 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/education/3c4e8a36-1b3e-4f21-bf5f-9e3f97c0d120/certificate - 201 - 95.05ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:19:44 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f, template: german
2025-07-10 17:19:44 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.04s
2025-07-10 17:19:44 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 17:19:44 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/export - 200 - 129.54ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:24:51 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:24:51 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:24:51 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:24:51 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:24:51 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:24:51 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:24:51 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:24:51 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:25:30 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:25:31 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:25:31 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:25:31 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:25:31 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:25:31 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:25:31 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:25:31 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:25:55 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:25:56 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:25:56 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:25:56 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:25:56 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:25:56 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:25:56 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:25:56 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:26:11 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:26:12 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:26:12 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:26:12 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:26:12 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:26:12 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:26:12 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:26:12 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:26:31 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:26:32 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:26:32 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:26:32 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:26:32 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:26:32 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:26:32 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:26:32 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:27:19 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:27:19 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:27:19 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:27:19 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:27:19 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:27:19 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:27:19 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:27:19 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:27:39 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:27:40 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:27:40 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:27:40 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:27:40 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:27:40 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:27:40 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:27:40 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:28:16 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:28:16 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:28:17 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:28:17 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:28:17 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:28:17 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:28:17 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:28:17 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:28:59 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:29:00 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:29:00 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:29:00 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:29:00 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:29:00 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:29:00 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:29:00 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:29:27 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:29:27 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:29:28 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:29:28 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:29:28 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:29:28 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:29:28 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:29:28 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:29:48 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:29:48 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:29:48 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:29:48 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:29:48 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:29:48 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:29:48 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:29:48 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:30:45 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:30:46 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:30:46 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:30:46 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:30:46 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:30:46 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:30:46 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:30:46 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:31:45 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:31:46 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:31:46 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:31:46 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:31:46 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:31:46 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:31:46 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:31:46 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:32:47 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:32:48 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:32:48 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:32:48 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:32:48 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:32:48 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:32:48 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:32:48 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:34:14 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 5.20ms - IP: 12*******
2025-07-10 17:34:14 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 168.40ms - IP: 12*******
2025-07-10 17:34:50 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 17:34:50 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 17:34:50 | WARNING  | app.core.dependencies:get_current_user_token:54 | Invalid or expired access token
2025-07-10 17:34:50 | ERROR    | app.core.dependencies:get_current_user_token:68 | Token validation error: 
2025-07-10 17:34:50 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 17:34:50 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Could not validate credentials - Status: 401
2025-07-10 17:34:50 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 401 - 88.47ms - IP: 12*******
2025-07-10 17:35:02 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:35:03 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:35:03 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:35:03 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:35:03 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:35:03 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:35:03 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:35:03 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:35:30 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 2.63ms - IP: 12*******
2025-07-10 17:35:31 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 68.66ms - IP: 12*******
2025-07-10 17:35:48 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-10 17:35:48 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 495.18ms - IP: 12*******
2025-07-10 17:39:45 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 72.20ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:40:19 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f, template: german
2025-07-10 17:40:19 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.03s
2025-07-10 17:40:19 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV e28f8d1f-0b4f-43c2-9b31-113f284cd40f <NAME_EMAIL>
2025-07-10 17:40:19 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/export - 200 - 240.37ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:41:08 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/files - 200 - 19.94ms - User: 0519388f-4db0-4fdf-ac70-d82cf78e042b - IP: 12*******
2025-07-10 17:58:17 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:58:17 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:58:17 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:58:17 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:58:17 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:58:17 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:58:17 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:58:17 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:58:38 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:58:38 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:58:38 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:58:38 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:58:38 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:58:38 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:58:38 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:58:38 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 17:59:08 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:59:08 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 17:59:09 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 17:59:09 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 17:59:09 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 17:59:09 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 17:59:09 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 17:59:09 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:00:20 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:00:20 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:00:20 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:00:20 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:00:20 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:00:20 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:00:20 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:00:20 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:01:02 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:01:02 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:01:02 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:01:02 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:01:02 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:01:02 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:01:02 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:01:02 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:02:36 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:02:37 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:02:37 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:02:37 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:02:37 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:02:37 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:02:37 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:02:37 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:03:09 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:03:10 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:03:10 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:03:10 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:03:10 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:03:10 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:03:10 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:03:10 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:03:32 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:03:32 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:03:32 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:03:32 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:03:32 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:03:32 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:03:32 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:03:32 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:03:45 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:03:46 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:03:46 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:03:46 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:03:46 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:03:46 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:03:46 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:03:46 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:04:58 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:04:58 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:04:59 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:04:59 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:04:59 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:04:59 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:04:59 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:04:59 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:05:46 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:05:46 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:05:46 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:05:46 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:05:46 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:05:46 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:05:46 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:05:46 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:06:00 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:06:00 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:06:00 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:06:00 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:06:00 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:06:00 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:06:00 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:06:00 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:06:37 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:06:37 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:06:37 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:06:37 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:06:37 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:06:37 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:06:37 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:06:37 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:07:17 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:07:18 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:07:18 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:07:18 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:07:18 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:07:18 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:07:18 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:07:18 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:08:57 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:08:58 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:08:58 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:08:58 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:08:58 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:08:58 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:08:58 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:08:58 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:09:59 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:09:59 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:09:59 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:09:59 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:09:59 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:09:59 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:09:59 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:09:59 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:11:14 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 18:11:14 | WARNING  | app.core.auth:verify_token:137 | JWT verification failed: Signature has expired.
2025-07-10 18:11:14 | WARNING  | app.core.dependencies:get_current_user_token:54 | Invalid or expired access token
2025-07-10 18:11:14 | ERROR    | app.core.dependencies:get_current_user_token:68 | Token validation error: 
2025-07-10 18:11:14 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 18:11:14 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Could not validate credentials - Status: 401
2025-07-10 18:11:14 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e28f8d1f-0b4f-43c2-9b31-113f284cd40f/files - 401 - 15.79ms - IP: 12*******
2025-07-10 18:12:00 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 18:12:00 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Invalid refresh token - Status: 401
2025-07-10 18:12:00 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/refresh-token - 401 - 71.64ms - IP: 12*******
2025-07-10 18:12:04 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 3.99ms - IP: 12*******
2025-07-10 18:12:05 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 73.34ms - IP: 12*******
2025-07-10 18:13:00 | WARNING  | app.endpoints.auth:login_user:124 | Login attempt with non-existent email: <EMAIL>
2025-07-10 18:13:00 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 18:13:00 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Invalid email or password - Status: 401
2025-07-10 18:13:00 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 401 - 21.99ms - IP: 12*******
2025-07-10 18:13:08 | WARNING  | app.endpoints.auth:login_user:124 | Login attempt with non-existent email: <EMAIL>
2025-07-10 18:13:08 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 18:13:08 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Invalid email or password - Status: 401
2025-07-10 18:13:08 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 401 - 14.63ms - IP: 12*******
2025-07-10 18:13:24 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:13:24 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:13:24 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:13:24 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:13:24 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:13:24 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:13:24 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:13:24 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:13:26 | WARNING  | app.endpoints.auth:login_user:124 | Login attempt with non-existent email: <EMAIL>
2025-07-10 18:13:26 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 18:13:26 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Invalid email or password - Status: 401
2025-07-10 18:13:26 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 401 - 67.49ms - IP: 12*******
2025-07-10 18:13:35 | WARNING  | app.endpoints.auth:login_user:124 | Login attempt with non-existent email: <EMAIL>
2025-07-10 18:13:35 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 18:13:35 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Invalid email or password - Status: 401
2025-07-10 18:13:35 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 401 - 63.26ms - IP: 12*******
2025-07-10 18:14:22 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:14:22 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:14:22 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:14:22 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:14:22 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:14:22 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:14:22 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:14:22 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:14:31 | WARNING  | app.endpoints.auth:login_user:124 | Login attempt with non-existent email: <EMAIL>
2025-07-10 18:14:31 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 18:14:31 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Invalid email or password - Status: 401
2025-07-10 18:14:31 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 401 - 46.43ms - IP: 12*******
2025-07-10 18:14:36 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 1.28ms - IP: 12*******
2025-07-10 18:14:37 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 74.42ms - IP: 12*******
2025-07-10 18:14:50 | WARNING  | app.endpoints.auth:login_user:124 | Login attempt with non-existent email: <EMAIL>
2025-07-10 18:14:50 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 18:14:50 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Invalid email or password - Status: 401
2025-07-10 18:14:50 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 401 - 63.53ms - IP: 12*******
2025-07-10 18:15:12 | WARNING  | app.endpoints.auth:login_user:124 | Login attempt with non-existent email: <EMAIL>
2025-07-10 18:15:12 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 18:15:12 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Invalid email or password - Status: 401
2025-07-10 18:15:12 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 401 - 7.04ms - IP: 12*******
2025-07-10 18:15:48 | WARNING  | app.endpoints.auth:login_user:124 | Login attempt with non-existent email: <EMAIL>
2025-07-10 18:15:48 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 18:15:48 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Invalid email or password - Status: 401
2025-07-10 18:15:48 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 401 - 13.81ms - IP: 12*******
2025-07-10 18:16:09 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:16:10 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-10 18:16:10 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-10 18:16:10 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-10 18:16:10 | INFO     | main:startup_event:64 | Starting CV Maker API...
2025-07-10 18:16:10 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-10 18:16:10 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-10 18:16:10 | INFO     | main:startup_event:66 | Database initialized successfully
2025-07-10 18:16:41 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-10 18:16:41 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 510.35ms - IP: 12*******
2025-07-10 18:17:06 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-10 18:17:06 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 400.43ms - IP: 12*******
2025-07-10 18:17:28 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 6.43ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:17:40 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/profile - 200 - 6.22ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:18:11 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 14.14ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:18:26 | INFO     | app.endpoints.cv:create_cv:83 | CV created: fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 <NAME_EMAIL>
2025-07-10 18:18:26 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 201 - 82.58ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:18:40 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 6.89ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:18:55 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 - 200 - 20.90ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:19:50 | INFO     | app.endpoints.cv:update_cv:259 | CV updated: fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 <NAME_EMAIL>
2025-07-10 18:19:50 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 - 200 - 95.69ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:20:12 | INFO     | app.endpoints.cv:update_personal_info:406 | Personal info updated for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 <NAME_EMAIL>
2025-07-10 18:20:12 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7/personal-info - 200 - 92.88ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:20:34 | INFO     | app.endpoints.cv:update_education:482 | Education updated for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 <NAME_EMAIL>
2025-07-10 18:20:34 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7/education - 200 - 90.29ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:21:03 | INFO     | app.endpoints.cv:update_work_experience:558 | Work experience updated for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 <NAME_EMAIL>
2025-07-10 18:21:03 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7/work-experience - 200 - 97.81ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:22:26 | INFO     | app.endpoints.cv:update_skills:634 | Skills updated for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 <NAME_EMAIL>
2025-07-10 18:22:27 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7/skills - 200 - 88.92ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:23:06 | INFO     | app.endpoints.cv:update_references:710 | References updated for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 <NAME_EMAIL>
2025-07-10 18:23:06 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7/references - 200 - 117.17ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:23:37 | INFO     | app.endpoints.cv:update_cover_letter:791 | Cover letter updated for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 <NAME_EMAIL>
2025-07-10 18:23:37 | INFO     | app.core.logging:log_api_access:179 | PUT /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7/cover-letter - 200 - 105.16ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:23:58 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7, template: german
2025-07-10 18:23:58 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.13s
2025-07-10 18:23:58 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 <NAME_EMAIL>
2025-07-10 18:23:58 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7/export - 200 - 338.69ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:27:14 | INFO     | app.services.file_validation_service:validate_file:121 | File validation passed: intro-photo-final.jpg
2025-07-10 18:27:14 | ERROR    | app.endpoints.files:upload_profile_photo:163 | Profile photo upload failed: 'url' is an invalid keyword argument for File
2025-07-10 18:27:14 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 18:27:14 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Failed to upload profile photo - Status: 500
2025-07-10 18:27:14 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7/photo - 500 - 90.22ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:28:48 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv - 200 - 77.23ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:29:14 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 - 200 - 66.19ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:30:02 | INFO     | app.services.file_validation_service:validate_file:121 | File validation passed: Daniel Aiyelu - Main_CV.pdf
2025-07-10 18:30:02 | ERROR    | app.endpoints.files:upload_certificate:303 | Certificate upload failed: 'url' is an invalid keyword argument for File
2025-07-10 18:30:02 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 18:30:02 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Failed to upload certificate - Status: 500
2025-07-10 18:30:02 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7/education/3c4e8a36-1b3e-4f21-bf5f-9e3f97c0d120/certificate - 500 - 78.72ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:32:36 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-10 18:32:36 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Template 'creative' not found - Status: 422
2025-07-10 18:32:36 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7/export - 422 - 10.22ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:32:45 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7, template: modern
2025-07-10 18:32:45 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.03s
2025-07-10 18:32:45 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 <NAME_EMAIL>
2025-07-10 18:32:45 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7/export - 200 - 81.77ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:33:05 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7, template: standard
2025-07-10 18:33:05 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.10s
2025-07-10 18:33:05 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 <NAME_EMAIL>
2025-07-10 18:33:05 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7/export - 200 - 129.74ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:33:47 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7, template: standard
2025-07-10 18:33:47 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.06s
2025-07-10 18:33:47 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 <NAME_EMAIL>
2025-07-10 18:33:47 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7/export - 200 - 165.00ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
2025-07-10 18:34:02 | INFO     | app.services.pdf_service:_generate_pdf_internal:177 | PDF generated for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7, template: german
2025-07-10 18:34:02 | INFO     | app.services.pdf_optimization_service:generate_with_monitoring:171 | PDF generated in 0.06s
2025-07-10 18:34:02 | INFO     | app.endpoints.cv:export_cv_pdf:896 | PDF exported for CV fad8eb5e-1a52-4102-a0c7-0b5a96a566d7 <NAME_EMAIL>
2025-07-10 18:34:02 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/fad8eb5e-1a52-4102-a0c7-0b5a96a566d7/export - 200 - 91.35ms - User: 8d1dd76e-dff0-4ebe-bf52-ead65573e10f - IP: 12*******
