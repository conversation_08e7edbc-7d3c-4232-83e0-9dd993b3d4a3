#!/usr/bin/env python3
"""
Database reset script with sample data.

This script drops all tables, recreates them, and inserts sample data
for testing the CV template system.
"""

import asyncio
import json
import sys
import os
from datetime import datetime, timed<PERSON><PERSON>

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from app.core.database import Base
from app.models.user import User
from app.models.cv import CV
from app.models.file import File
from app.models.user_activity import UserActivity
from app.core.auth import get_password_hash

# Use SQLite database for reset
DATABASE_URL = "sqlite+aiosqlite:///./cv_maker.db"


async def reset_database():
    """Reset database and create sample data."""
    print("🔄 Resetting database...")
    
    # Create async engine
    engine = create_async_engine(DATABASE_URL, echo=True)
    
    # Drop all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
        print("✅ Dropped all tables")
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
        print("✅ Created all tables")
    
    # Create session
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    async with async_session() as session:
        # Create sample user
        sample_user = User(
            email="<EMAIL>",
            password=get_password_hash("password123"),
            name="Max Mustermann",
            language="de"
        )
        session.add(sample_user)
        await session.commit()
        await session.refresh(sample_user)
        print(f"✅ Created sample user: {sample_user.email}")
        
        # Create sample CV with comprehensive data
        sample_cv = CV(
            user_id=sample_user.id,
            title="Senior Software Developer",
            template="german",
            language="de",
            personal_info={
                "firstName": "Max",
                "lastName": "Mustermann",
                "email": "<EMAIL>",
                "phone": "+49 123 456789",
                "address": "Musterstraße 123",
                "city": "Berlin",
                "postalCode": "10115",
                "country": "Deutschland",
                "dateOfBirth": "1990-01-15",
                "placeOfBirth": "Hamburg",
                "nationality": "Deutsch",
                "maritalStatus": "Ledig",
                "summary": "Erfahrener Software-Entwickler mit über 5 Jahren Berufserfahrung in der Entwicklung von Web-Anwendungen und Backend-Systemen. Spezialisiert auf Python, JavaScript und moderne Frameworks.",
                "photoUrl": None  # Will be set when photo is uploaded
            },
            education=[
                {
                    "id": "edu_001",
                    "institution": "Technische Universität Berlin",
                    "degree": "Master of Science Informatik",
                    "fieldOfStudy": "Computer Science",
                    "startDate": "2018-10-01",
                    "endDate": "2020-09-30",
                    "isCurrentlyStudying": False,
                    "grade": "1.3",
                    "description": "Schwerpunkt: Software Engineering, Künstliche Intelligenz und Datenbanksysteme. Masterarbeit über 'Optimierung von Machine Learning Algorithmen für große Datenmengen'.",
                    "certificates": []  # Will be populated when certificates are uploaded
                },
                {
                    "id": "edu_002",
                    "institution": "Universität Hamburg",
                    "degree": "Bachelor of Science Informatik",
                    "fieldOfStudy": "Computer Science",
                    "startDate": "2015-10-01",
                    "endDate": "2018-07-31",
                    "isCurrentlyStudying": False,
                    "grade": "1.8",
                    "description": "Grundstudium in Informatik mit Nebenfach Mathematik. Bachelorarbeit über 'Entwicklung einer Web-Anwendung für Projektmanagement'.",
                    "certificates": []
                }
            ],
            work_experience=[
                {
                    "id": "work_001",
                    "company": "Tech Solutions GmbH",
                    "position": "Senior Software Developer",
                    "startDate": "2022-03-01",
                    "endDate": None,
                    "isCurrentlyWorking": True,
                    "description": "Entwicklung und Wartung von Web-Anwendungen mit Python/Django und React. Führung eines 4-köpfigen Entwicklerteams. Implementierung von CI/CD-Pipelines und Code-Review-Prozessen."
                },
                {
                    "id": "work_002",
                    "company": "StartupXYZ",
                    "position": "Full-Stack Developer",
                    "startDate": "2020-10-01",
                    "endDate": "2022-02-28",
                    "isCurrentlyWorking": False,
                    "description": "Vollständige Entwicklung einer E-Commerce-Plattform von der Konzeption bis zur Produktionsreife. Verwendung von Node.js, React und PostgreSQL."
                },
                {
                    "id": "work_003",
                    "company": "WebDev Agency",
                    "position": "Junior Developer",
                    "startDate": "2018-08-01",
                    "endDate": "2020-09-30",
                    "isCurrentlyWorking": False,
                    "description": "Entwicklung von Websites und kleinen Web-Anwendungen für verschiedene Kunden. Erste Erfahrungen mit agilen Entwicklungsmethoden."
                }
            ],
            skills=[
                {
                    "id": "skill_001",
                    "name": "Python",
                    "category": "technical",
                    "level": "expert"
                },
                {
                    "id": "skill_002",
                    "name": "JavaScript",
                    "category": "technical",
                    "level": "advanced"
                },
                {
                    "id": "skill_003",
                    "name": "React",
                    "category": "technical",
                    "level": "advanced"
                },
                {
                    "id": "skill_004",
                    "name": "Django",
                    "category": "technical",
                    "level": "expert"
                },
                {
                    "id": "skill_005",
                    "name": "PostgreSQL",
                    "category": "technical",
                    "level": "intermediate"
                },
                {
                    "id": "skill_006",
                    "name": "Docker",
                    "category": "technical",
                    "level": "intermediate"
                },
                {
                    "id": "skill_007",
                    "name": "Deutsch",
                    "category": "language",
                    "level": "native"
                },
                {
                    "id": "skill_008",
                    "name": "Englisch",
                    "category": "language",
                    "level": "advanced"
                },
                {
                    "id": "skill_009",
                    "name": "Teamarbeit",
                    "category": "soft",
                    "level": "expert"
                },
                {
                    "id": "skill_010",
                    "name": "Problemlösung",
                    "category": "soft",
                    "level": "advanced"
                }
            ],
            references=[
                {
                    "id": "ref_001",
                    "name": "Dr. Anna Schmidt",
                    "position": "Team Lead",
                    "company": "Tech Solutions GmbH",
                    "email": "<EMAIL>",
                    "phone": "+49 **********"
                },
                {
                    "id": "ref_002",
                    "name": "Michael Weber",
                    "position": "CTO",
                    "company": "StartupXYZ",
                    "email": "<EMAIL>",
                    "phone": "+49 **********"
                }
            ],
            cover_letter=json.dumps({
                "recipientName": "Frau Dr. Müller",
                "company": "Innovation Tech AG",
                "address": "Technologiepark 1",
                "postalCode": "80333",
                "city": "München",
                "country": "Deutschland",
                "email": "<EMAIL>",
                "phone": "+49 89 123456",
                "otherInformation": "Personalabteilung",
                "subject": "Bewerbung als Senior Software Developer",
                "date": datetime.now().strftime('%Y-%m-%d'),
                "content": """Sehr geehrte Frau Dr. Müller,

mit großem Interesse habe ich Ihre Stellenausschreibung für die Position als Senior Software Developer gelesen. Als erfahrener Entwickler mit über 5 Jahren Berufserfahrung in der Softwareentwicklung möchte ich mich gerne bei Ihnen bewerben.

In meiner aktuellen Position bei der Tech Solutions GmbH leite ich ein Team von vier Entwicklern und bin verantwortlich für die Entwicklung und Wartung komplexer Web-Anwendungen. Dabei setze ich hauptsächlich Python mit Django sowie React für Frontend-Entwicklung ein. Besonders stolz bin ich auf die erfolgreiche Implementierung einer CI/CD-Pipeline, die unsere Deployment-Zeit um 60% reduziert hat.

Ihre Stellenausschreibung hat mich besonders angesprochen, da Sie innovative Technologien einsetzen und Wert auf agile Entwicklungsmethoden legen. Meine Erfahrung in der Vollstack-Entwicklung und meine Führungsqualitäten würden perfekt zu Ihrem Team passen.

Gerne würde ich Sie in einem persönlichen Gespräch von meinen Fähigkeiten überzeugen und freue mich auf Ihre Rückmeldung.

Mit freundlichen Grüßen""",
                "signatureFileId": None  # Will be set when signature is uploaded
            })
        )
        
        session.add(sample_cv)
        await session.commit()
        await session.refresh(sample_cv)
        print(f"✅ Created sample CV: {sample_cv.title}")
        
        # Create some sample activities
        activities = [
            UserActivity.create_activity(
                user_id=sample_user.id,
                action_type="create_cv",
                endpoint="/api/v1/cv",
                details={"cv_id": sample_cv.id, "title": sample_cv.title}
            ),
            UserActivity.create_activity(
                user_id=sample_user.id,
                action_type="update_personal_info",
                endpoint=f"/api/v1/cv/{sample_cv.id}/personal-info",
                details={"cv_id": sample_cv.id, "updated_fields": ["firstName", "lastName", "email"]}
            ),
            UserActivity.create_activity(
                user_id=sample_user.id,
                action_type="update_skills",
                endpoint=f"/api/v1/cv/{sample_cv.id}/skills",
                details={"cv_id": sample_cv.id, "entries_count": 10}
            )
        ]
        
        for activity in activities:
            session.add(activity)
        
        await session.commit()
        print(f"✅ Created {len(activities)} sample activities")
        
        print("\n🎉 Database reset complete!")
        print("\n📋 Sample Data Created:")
        print(f"👤 User: {sample_user.email} (password: password123)")
        print(f"📄 CV: {sample_cv.title} (ID: {sample_cv.id})")
        print(f"🎨 Template: {sample_cv.template}")
        print(f"🌍 Language: {sample_cv.language}")
        print(f"📚 Education entries: {len(sample_cv.education)}")
        print(f"💼 Work experience entries: {len(sample_cv.work_experience)}")
        print(f"🛠️ Skills: {len(sample_cv.skills)}")
        print(f"👥 References: {len(sample_cv.references)}")
        print(f"✉️ Cover letter: ✅")
        print(f"📊 Activities: {len(activities)}")
        
        print("\n🔗 API Endpoints to Test:")
        print(f"📋 Get templates: GET /api/v1/templates")
        print(f"📄 Get CV: GET /api/v1/cv/{sample_cv.id}")
        print(f"📥 Export PDF: GET /api/v1/cv/{sample_cv.id}/export?template_id=german")
        print(f"🖼️ Upload photo: POST /api/v1/cv/{sample_cv.id}/photo")
        print(f"📜 Upload certificate: POST /api/v1/cv/{sample_cv.id}/education/edu_001/certificate")
        
        print("\n🔑 Authentication:")
        print("Use POST /api/v1/auth/login with:")
        print("  Email: <EMAIL>")
        print("  Password: password123")
    
    await engine.dispose()


if __name__ == "__main__":
    asyncio.run(reset_database())
