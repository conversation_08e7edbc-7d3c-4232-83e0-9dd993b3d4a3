# CV Sample Data - Auto-Generated IDs

All CV sections now auto-generate unique IDs on create/update operations. You don't need to provide IDs in your requests - they will be automatically generated.

## 1. Personal Info Update

**Endpoint**: `PUT /api/v1/cv/{cv_id}/personal-info`

```json
{
  "firstName": "Max",
  "lastName": "<PERSON>ermann",
  "email": "<EMAIL>",
  "phone": "+49 123 456789",
  "address": "Musterstraße 123",
  "city": "Berlin",
  "postalCode": "10115",
  "country": "Deutschland",
  "dateOfBirth": "1990-01-15",
  "placeOfBirth": "Hamburg",
  "nationality": "Deutsch",
  "maritalStatus": "Ledig",
  "summary": "Erfahrener Software-Entwickler mit über 5 Jahren Berufserfahrung in der Entwicklung von Web-Anwendungen und Backend-Systemen. Spezialisiert auf Python, JavaScript und moderne Frameworks."
}
```

## 2. Education Update

**Endpoint**: `PUT /api/v1/cv/{cv_id}/education`

```json
{
  "education": [
    {
      "institution": "Technische Universität Berlin",
      "degree": "Master of Science Informatik",
      "fieldOfStudy": "Computer Science",
      "startDate": "2018-10-01",
      "endDate": "2020-09-30",
      "isCurrentlyStudying": false,
      "grade": "1.3",
      "description": "Schwerpunkt: Software Engineering, Künstliche Intelligenz und Datenbanksysteme. Masterarbeit über 'Optimierung von Machine Learning Algorithmen für große Datenmengen'.",
      "certificates": []
    },
    {
      "institution": "Universität Hamburg",
      "degree": "Bachelor of Science Informatik",
      "fieldOfStudy": "Computer Science",
      "startDate": "2015-10-01",
      "endDate": "2018-07-31",
      "isCurrentlyStudying": false,
      "grade": "1.8",
      "description": "Grundstudium in Informatik mit Nebenfach Mathematik. Bachelorarbeit über 'Entwicklung einer Web-Anwendung für Projektmanagement'.",
      "certificates": []
    },
    {
      "institution": "Max-Planck-Gymnasium",
      "degree": "Abitur",
      "fieldOfStudy": "Naturwissenschaften",
      "startDate": "2007-08-01",
      "endDate": "2015-06-30",
      "isCurrentlyStudying": false,
      "grade": "1.5",
      "description": "Leistungskurse: Mathematik und Physik. Besondere Auszeichnung für hervorragende Leistungen in MINT-Fächern.",
      "certificates": []
    }
  ]
}
```

## 3. Work Experience Update

**Endpoint**: `PUT /api/v1/cv/{cv_id}/work-experience`

```json
{
  "workExperience": [
    {
      "company": "Tech Solutions GmbH",
      "position": "Senior Software Developer",
      "startDate": "2022-03-01",
      "endDate": null,
      "isCurrentlyWorking": true,
      "description": "Entwicklung und Wartung von Web-Anwendungen mit Python/Django und React. Führung eines 4-köpfigen Entwicklerteams. Implementierung von CI/CD-Pipelines und Code-Review-Prozessen. Verantwortlich für die Architektur und Skalierung von Microservices.",
      "location": "Berlin, Deutschland"
    },
    {
      "company": "StartupXYZ",
      "position": "Full-Stack Developer",
      "startDate": "2020-10-01",
      "endDate": "2022-02-28",
      "isCurrentlyWorking": false,
      "description": "Vollständige Entwicklung einer E-Commerce-Plattform von der Konzeption bis zur Produktionsreife. Verwendung von Node.js, React und PostgreSQL. Implementierung von Zahlungssystemen und Bestandsverwaltung.",
      "location": "München, Deutschland"
    },
    {
      "company": "WebDev Agency",
      "position": "Junior Developer",
      "startDate": "2018-08-01",
      "endDate": "2020-09-30",
      "isCurrentlyWorking": false,
      "description": "Entwicklung von Websites und kleinen Web-Anwendungen für verschiedene Kunden. Erste Erfahrungen mit agilen Entwicklungsmethoden. Zusammenarbeit mit Designern und Projektmanagern.",
      "location": "Hamburg, Deutschland"
    }
  ]
}
```

## 4. Skills Update

**Endpoint**: `PUT /api/v1/cv/{cv_id}/skills`

```json
{
  "skills": [
    {
      "name": "Python",
      "category": "technical",
      "level": "expert"
    },
    {
      "name": "JavaScript",
      "category": "technical",
      "level": "advanced"
    },
    {
      "name": "React",
      "category": "technical",
      "level": "advanced"
    },
    {
      "name": "Django",
      "category": "technical",
      "level": "expert"
    },
    {
      "name": "Node.js",
      "category": "technical",
      "level": "intermediate"
    },
    {
      "name": "PostgreSQL",
      "category": "technical",
      "level": "intermediate"
    },
    {
      "name": "Docker",
      "category": "technical",
      "level": "intermediate"
    },
    {
      "name": "AWS",
      "category": "technical",
      "level": "beginner"
    },
    {
      "name": "Deutsch",
      "category": "language",
      "level": "native"
    },
    {
      "name": "Englisch",
      "category": "language",
      "level": "advanced"
    },
    {
      "name": "Französisch",
      "category": "language",
      "level": "beginner"
    },
    {
      "name": "Teamarbeit",
      "category": "soft",
      "level": "expert"
    },
    {
      "name": "Problemlösung",
      "category": "soft",
      "level": "advanced"
    },
    {
      "name": "Projektmanagement",
      "category": "soft",
      "level": "intermediate"
    },
    {
      "name": "Kommunikation",
      "category": "soft",
      "level": "advanced"
    }
  ]
}
```

## 5. References Update

**Endpoint**: `PUT /api/v1/cv/{cv_id}/references`

```json
{
  "references": [
    {
      "name": "Dr. Anna Schmidt",
      "position": "Team Lead",
      "company": "Tech Solutions GmbH",
      "email": "<EMAIL>",
      "phone": "+49 **********"
    },
    {
      "name": "Michael Weber",
      "position": "CTO",
      "company": "StartupXYZ",
      "email": "<EMAIL>",
      "phone": "+49 **********"
    },
    {
      "name": "Prof. Dr. Thomas Müller",
      "position": "Professor für Informatik",
      "company": "Technische Universität Berlin",
      "email": "<EMAIL>",
      "phone": "+49 30 314 12345"
    }
  ]
}
```

## 6. Cover Letter Update

**Endpoint**: `PUT /api/v1/cv/{cv_id}/cover-letter`

```json
{
  "coverLetter": {
    "recipientName": "Frau Dr. Müller",
    "company": "Innovation Tech AG",
    "address": "Technologiepark 1",
    "postalCode": "80333",
    "city": "München",
    "country": "Deutschland",
    "email": "<EMAIL>",
    "phone": "+49 89 123456",
    "otherInformation": "Personalabteilung",
    "subject": "Bewerbung als Senior Software Developer",
    "date": "2025-07-10",
    "content": "Sehr geehrte Frau Dr. Müller,\n\nmit großem Interesse habe ich Ihre Stellenausschreibung für die Position als Senior Software Developer gelesen. Als erfahrener Entwickler mit über 5 Jahren Berufserfahrung in der Softwareentwicklung möchte ich mich gerne bei Ihnen bewerben.\n\nIn meiner aktuellen Position bei der Tech Solutions GmbH leite ich ein Team von vier Entwicklern und bin verantwortlich für die Entwicklung und Wartung komplexer Web-Anwendungen. Dabei setze ich hauptsächlich Python mit Django sowie React für Frontend-Entwicklung ein. Besonders stolz bin ich auf die erfolgreiche Implementierung einer CI/CD-Pipeline, die unsere Deployment-Zeit um 60% reduziert hat.\n\nIhre Stellenausschreibung hat mich besonders angesprochen, da Sie innovative Technologien einsetzen und Wert auf agile Entwicklungsmethoden legen. Meine Erfahrung in der Vollstack-Entwicklung und meine Führungsqualitäten würden perfekt zu Ihrem Team passen.\n\nGerne würde ich Sie in einem persönlichen Gespräch von meinen Fähigkeiten überzeugen und freue mich auf Ihre Rückmeldung.\n\nMit freundlichen Grüßen",
    "signatureFileId": null
  }
}
```

## 7. CV Creation

**Endpoint**: `POST /api/v1/cv`

```json
{
  "title": "Senior Software Developer",
  "template": "german",
  "language": "de"
}
```

## Key Features

### ✅ **Auto-Generated IDs**
- All CV sections (education, work experience, skills, references) automatically generate unique UUIDs
- No need to provide IDs in requests - they're handled server-side
- Existing IDs are preserved if provided

### ✅ **Validation Categories**
- **Skills Categories**: `technical`, `language`, `soft`
- **Skills Levels**: `beginner`, `intermediate`, `advanced`, `expert`, `native`
- **Templates**: `standard`, `modern`, `creative`, `german-ausbildung`, `german`
- **Languages**: `en`, `de`

### ✅ **File Integration**
- Profile photos: Upload via `POST /api/v1/cv/{cv_id}/photo`
- Certificates: Upload via `POST /api/v1/cv/{cv_id}/education/{edu_id}/certificate`
- Signatures: Upload for cover letter signatures

### ✅ **German Template Features**
- Multi-page layout with cover page
- Table of contents
- Professional German formatting
- Certificate integration
- Cover letter support

All endpoints now support auto-ID generation for consistent, unique identifiers across all CV sections!
