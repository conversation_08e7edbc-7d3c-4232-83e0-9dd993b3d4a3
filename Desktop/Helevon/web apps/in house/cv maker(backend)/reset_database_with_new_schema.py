#!/usr/bin/env python3
"""
Database reset and migration script for new file management system.

This script:
1. Backs up existing data
2. Drops and recreates the database with new schema
3. Creates comprehensive sample data with photos and certificates
4. Tests the new file management system
"""

import asyncio
import os
import sys
import json
import base64
from datetime import datetime, timedelta
from pathlib import Path

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text

from app.core.database import Base
from app.core.config import settings
from app.models.user import User
from app.models.cv import CV
from app.models.file import File
from app.models.user_activity import UserActivity
from app.core.auth import get_password_hash
from loguru import logger


class DatabaseMigration:
    """Database migration and reset service."""
    
    def __init__(self):
        self.db_url = settings.get_database_url()
        self.engine = create_async_engine(self.db_url, echo=False)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )
    
    async def backup_existing_data(self):
        """Backup existing data before migration."""
        logger.info("🔄 Backing up existing data...")
        
        backup_data = {
            'users': [],
            'cvs': [],
            'files': [],
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            async with self.async_session() as session:
                # Backup users
                result = await session.execute(text("SELECT * FROM users"))
                users = result.fetchall()
                backup_data['users'] = [dict(row._mapping) for row in users]
                
                # Backup CVs
                result = await session.execute(text("SELECT * FROM cvs"))
                cvs = result.fetchall()
                backup_data['cvs'] = [dict(row._mapping) for row in cvs]
                
                # Backup files
                result = await session.execute(text("SELECT * FROM files"))
                files = result.fetchall()
                backup_data['files'] = [dict(row._mapping) for row in files]
                
                logger.info(f"✅ Backed up {len(backup_data['users'])} users, {len(backup_data['cvs'])} CVs, {len(backup_data['files'])} files")
                
        except Exception as e:
            logger.warning(f"⚠️ Could not backup existing data (database may not exist): {e}")
        
        # Save backup to file
        backup_file = f"database_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(backup_file, 'w') as f:
            json.dump(backup_data, f, indent=2, default=str)
        
        logger.info(f"💾 Backup saved to {backup_file}")
        return backup_data
    
    async def drop_and_recreate_database(self):
        """Drop all tables and recreate with new schema."""
        logger.info("🗑️ Dropping existing tables...")
        
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
            logger.info("✅ All tables dropped")
            
            logger.info("🏗️ Creating new tables with updated schema...")
            await conn.run_sync(Base.metadata.create_all)
            logger.info("✅ New tables created")
    
    async def create_sample_data(self):
        """Create comprehensive sample data with photos and certificates."""
        logger.info("📝 Creating sample data...")
        
        async with self.async_session() as session:
            # Create sample user
            user = User(
                email="<EMAIL>",
                password=get_password_hash("password123"),
                name="Daniel Aiyelu"
            )
            session.add(user)
            await session.flush()  # Get user ID
            
            # Create sample CV
            cv = CV(
                user_id=user.id,
                title="Senior Software Developer CV",
                template="german-ausbildung",
                language="de"
            )
            cv.initialize_empty_sections()
            
            # Add comprehensive personal info
            cv.personal_info = {
                "firstName": "Daniel",
                "lastName": "Aiyelu",
                "email": "<EMAIL>",
                "phone": "+44 7911 123456",
                "address": "123 Innovation Street",
                "city": "London",
                "postalCode": "E1 6AN",
                "country": "United Kingdom",
                "dateOfBirth": "1990-06-15",
                "placeOfBirth": "Manchester",
                "nationality": "British",
                "maritalStatus": "Single",
                "photoUrl": None,  # Will be set when photo is created
                "summary": "Experienced software engineer with a passion for developing scalable web applications and working across the full stack."
            }
            
            # Add education entries with unique IDs
            cv.education = [
                {
                    "id": "edu_001",
                    "institution": "University of Oxford",
                    "degree": "Bachelor of Science",
                    "fieldOfStudy": "Computer Science",
                    "startDate": "2008-09-01",
                    "endDate": "2011-06-30",
                    "isCurrentlyStudying": False,
                    "grade": "First Class Honours",
                    "description": "Focused on algorithms, distributed systems, and software engineering.",
                    "certificates": []  # Will be populated when certificates are created
                },
                {
                    "id": "edu_002",
                    "institution": "Imperial College London",
                    "degree": "Master of Science",
                    "fieldOfStudy": "Artificial Intelligence",
                    "startDate": "2012-09-01",
                    "endDate": "2013-09-01",
                    "isCurrentlyStudying": False,
                    "grade": "Distinction",
                    "description": "Specialized in deep learning and natural language processing.",
                    "certificates": []
                },
                {
                    "id": "edu_003",
                    "institution": "Coursera / Stanford University",
                    "degree": "Professional Certificate",
                    "fieldOfStudy": "Machine Learning",
                    "startDate": "2020-01-15",
                    "endDate": "2020-05-15",
                    "isCurrentlyStudying": False,
                    "grade": "Completed",
                    "description": "Online course led by Andrew Ng covering foundational ML concepts.",
                    "certificates": []
                }
            ]
            
            # Add work experience
            cv.work_experience = [
                {
                    "id": "work_001",
                    "company": "TechCorp Solutions",
                    "position": "Senior Software Engineer",
                    "startDate": "2018-03-01",
                    "endDate": None,
                    "isCurrentlyWorking": True,
                    "description": "Lead development of microservices architecture, mentor junior developers, and implement CI/CD pipelines."
                },
                {
                    "id": "work_002",
                    "company": "StartupXYZ",
                    "position": "Full Stack Developer",
                    "startDate": "2015-06-01",
                    "endDate": "2018-02-28",
                    "isCurrentlyWorking": False,
                    "description": "Developed web applications using React, Node.js, and PostgreSQL. Implemented real-time features with WebSocket."
                }
            ]
            
            # Add skills
            cv.skills = [
                {"id": "skill_001", "name": "Python", "category": "technical", "level": "expert"},
                {"id": "skill_002", "name": "JavaScript", "category": "technical", "level": "advanced"},
                {"id": "skill_003", "name": "React", "category": "technical", "level": "advanced"},
                {"id": "skill_004", "name": "PostgreSQL", "category": "technical", "level": "intermediate"},
                {"id": "skill_005", "name": "German", "category": "language", "level": "native"},
                {"id": "skill_006", "name": "English", "category": "language", "level": "native"},
                {"id": "skill_007", "name": "Team Leadership", "category": "soft", "level": "advanced"},
                {"id": "skill_008", "name": "Project Management", "category": "soft", "level": "intermediate"}
            ]
            
            session.add(cv)
            await session.flush()  # Get CV ID
            
            # Create sample profile photo (1x1 pixel PNG)
            photo_data = base64.b64decode(
                "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg=="
            )
            
            photo_file = File.create_photo(
                user_id=user.id,
                cv_id=cv.id,
                name="profile_photo.png",
                file_data=photo_data,
                mime_type="image/png"
            )
            session.add(photo_file)
            await session.flush()
            
            # Link photo to CV
            cv.personal_info["photoUrl"] = photo_file.id
            
            # Create sample certificates for education entries
            cert_data = b"Sample PDF certificate content"  # In real scenario, this would be actual PDF data
            
            # Certificate for Bachelor's degree
            cert1 = File.create_education_certificate(
                user_id=user.id,
                cv_id=cv.id,
                education_entry_id="edu_001",
                name="bachelor_degree_certificate.pdf",
                file_data=cert_data,
                mime_type="application/pdf"
            )
            session.add(cert1)
            await session.flush()
            
            # Certificate for Master's degree
            cert2 = File.create_education_certificate(
                user_id=user.id,
                cv_id=cv.id,
                education_entry_id="edu_002",
                name="master_degree_certificate.pdf",
                file_data=cert_data,
                mime_type="application/pdf"
            )
            session.add(cert2)
            await session.flush()
            
            # Standalone certificate
            cert3 = File.create_standalone_certificate(
                user_id=user.id,
                cv_id=cv.id,
                name="aws_certification.pdf",
                file_data=cert_data,
                mime_type="application/pdf",
                description="AWS Solutions Architect Professional Certification",
                issue_date=datetime(2023, 6, 15),
                issuing_organization="Amazon Web Services"
            )
            session.add(cert3)
            await session.flush()
            
            # Link certificates to education entries
            cv.education[0]["certificates"] = [cert1.id]
            cv.education[1]["certificates"] = [cert2.id]
            
            # Force SQLAlchemy to detect changes
            from sqlalchemy.orm import attributes
            attributes.flag_modified(cv, 'personal_info')
            attributes.flag_modified(cv, 'education')
            
            await session.commit()
            
            logger.info(f"✅ Created sample data:")
            logger.info(f"   👤 User: {user.email}")
            logger.info(f"   📄 CV: {cv.title}")
            logger.info(f"   📸 Photo: {photo_file.name}")
            logger.info(f"   📜 Certificates: {cert1.name}, {cert2.name}, {cert3.name}")
            
            return {
                'user_id': user.id,
                'cv_id': cv.id,
                'photo_id': photo_file.id,
                'certificate_ids': [cert1.id, cert2.id, cert3.id]
            }
    
    async def test_file_retrieval(self, sample_data):
        """Test that files can be retrieved properly."""
        logger.info("🧪 Testing file retrieval...")
        
        async with self.async_session() as session:
            # Test photo retrieval
            photo = await session.get(File, sample_data['photo_id'])
            assert photo is not None, "Photo not found"
            assert photo.is_photo, "Photo category incorrect"
            assert isinstance(photo.file_data, bytes), "Photo data should be bytes"
            logger.info(f"✅ Photo retrieval test passed: {photo.name}")
            
            # Test certificate retrieval
            for cert_id in sample_data['certificate_ids']:
                cert = await session.get(File, cert_id)
                assert cert is not None, f"Certificate {cert_id} not found"
                assert cert.is_certificate, "Certificate category incorrect"
                assert isinstance(cert.file_data, bytes), "Certificate data should be bytes"
                logger.info(f"✅ Certificate retrieval test passed: {cert.name}")
            
            # Test CV with linked files
            cv = await session.get(CV, sample_data['cv_id'])
            assert cv is not None, "CV not found"
            assert cv.personal_info.get('photoUrl') == sample_data['photo_id'], "Photo not linked to CV"
            
            # Check education certificates
            edu_with_certs = [edu for edu in cv.education if edu.get('certificates')]
            assert len(edu_with_certs) == 2, "Education certificates not linked properly"
            logger.info(f"✅ CV file linking test passed")
    
    async def run_migration(self):
        """Run the complete migration process."""
        logger.info("🚀 Starting database migration...")
        
        try:
            # Step 1: Backup existing data
            backup_data = await self.backup_existing_data()
            
            # Step 2: Drop and recreate database
            await self.drop_and_recreate_database()
            
            # Step 3: Create sample data
            sample_data = await self.create_sample_data()
            
            # Step 4: Test file retrieval
            await self.test_file_retrieval(sample_data)
            
            logger.info("🎉 Database migration completed successfully!")
            logger.info("📊 Summary:")
            logger.info(f"   🗄️ Database: Reset with new schema")
            logger.info(f"   👤 Sample user: <EMAIL> (password: password123)")
            logger.info(f"   📄 Sample CV: Senior Software Developer CV")
            logger.info(f"   📸 Profile photo: Linked and ready")
            logger.info(f"   📜 Certificates: 3 certificates (2 education-linked, 1 standalone)")
            logger.info("🚀 Ready for testing!")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Migration failed: {e}")
            raise
        finally:
            await self.engine.dispose()


async def main():
    """Main migration function."""
    migration = DatabaseMigration()
    await migration.run_migration()


if __name__ == "__main__":
    asyncio.run(main())
