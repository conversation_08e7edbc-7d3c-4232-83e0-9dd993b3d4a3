# CV Maker API

A comprehensive FastAPI backend for CV creation, management, and export with OAuth2-like authentication, file management, and analytics.

## Features

- **OAuth2-like Authentication**: Access and refresh tokens with secure password hashing
- **CV Management**: Complete CRUD operations for CVs with multiple templates
- **File Management**: Upload, store, and retrieve files with base64 encoding
- **PDF Export**: Generate professional PDFs with multiple templates and languages
- **User Activity Tracking**: Comprehensive metrics and analytics
- **Multi-language Support**: English, German, and Arabic
- **Security**: Rate limiting, security headers, and comprehensive error handling
- **Database Support**: SQLite for development, PostgreSQL for production

## Tech Stack

- **FastAPI**: Modern, fast web framework for building APIs
- **SQLAlchemy**: Async ORM with PostgreSQL and SQLite support
- **Pydantic v2**: Data validation and serialization
- **JWT**: JSON Web Tokens for authentication
- **ReportLab**: PDF generation
- **Loguru**: Structured logging
- **Typer**: CLI management commands

## Quick Start

### Prerequisites

- Python 3.8+
- Virtual environment (recommended)
- PostgreSQL (for production)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd cv-maker-backend
   ```

2. **Create and activate virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Initialize database**
   ```bash
   python manage.py migrate
   ```

6. **Run development server**
   ```bash
   python manage.py runserver
   ```

The API will be available at `http://localhost:8000`

## Management Commands

The `manage.py` script provides comprehensive management commands:

### Development Server
```bash
python manage.py runserver [OPTIONS]
```
- Uses SQLite database
- Auto-reload enabled
- Debug mode active

### Production Server
```bash
python manage.py runprod [OPTIONS]
```
- Uses PostgreSQL database
- Gunicorn with multiple workers
- Production optimizations

### Database Migration
```bash
python manage.py migrate [OPTIONS]
```
- Create/update database tables
- Support for both SQLite and PostgreSQL

### Other Commands
```bash
python manage.py shell      # Interactive Python shell
python manage.py test       # Run test suite
python manage.py logs       # View application logs
python manage.py version    # Show version info
```

## API Documentation

### Authentication Endpoints

- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/signin` - User login
- `POST /api/v1/auth/signout` - User logout
- `POST /api/v1/auth/refresh-token` - Refresh access token
- `POST /api/v1/auth/verify-password` - Verify password

### User Management

- `GET /api/v1/user/account` - Get user account
- `PUT /api/v1/user/account` - Update user account
- `DELETE /api/v1/user/account` - Delete user account

### CV Management

- `POST /api/v1/cv` - Create CV
- `GET /api/v1/cv` - List user CVs
- `GET /api/v1/cv/{cv_id}` - Get specific CV
- `PUT /api/v1/cv/{cv_id}` - Update CV
- `DELETE /api/v1/cv/{cv_id}` - Delete CV

### CV Sections

- `PUT /api/v1/cv/{cv_id}/personal-info` - Update personal information
- `PUT /api/v1/cv/{cv_id}/education` - Update education section
- `PUT /api/v1/cv/{cv_id}/work-experience` - Update work experience
- `PUT /api/v1/cv/{cv_id}/skills` - Update skills section
- `PUT /api/v1/cv/{cv_id}/references` - Update references
- `PUT /api/v1/cv/{cv_id}/cover-letter` - Update cover letter

### File Management

- `POST /api/v1/cv/{cv_id}/upload` - Upload file
- `GET /api/v1/cv/{cv_id}/file/{file_id}` - Get file
- `GET /api/v1/cv/{cv_id}/files` - List CV files
- `DELETE /api/v1/cv/{cv_id}/file/{file_id}` - Delete file

### PDF Export

- `GET /api/v1/cv/{cv_id}/export` - Export CV as PDF

### Metrics & Analytics

- `GET /api/v1/metrics/login-frequencies` - Login frequency metrics
- `GET /api/v1/metrics/action-frequencies` - Action frequency metrics
- `GET /api/v1/metrics/popular-actions` - Popular actions
- `GET /api/v1/metrics/summary` - Activity summary
- `GET /api/v1/metrics/comprehensive` - All metrics

## Configuration

### Environment Variables

```bash
# Application Settings
DEBUG=true
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database Settings
DATABASE_URL=sqlite+aiosqlite:///./cv_maker.db
DATABASE_URL_PROD=postgresql+asyncpg://user:password@localhost:5432/cvmaker

# CORS Settings
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8000

# File Upload Settings
MAX_UPLOAD_SIZE_MB=5
ALLOWED_FILE_TYPES=image/jpeg,image/png,application/pdf

# Server Settings
HOST=0.0.0.0
PORT=8000
WORKERS=4
```

## Testing

### Run Tests
```bash
# All tests
python manage.py test

# With coverage
python manage.py test --coverage

# Specific test pattern
python manage.py test --pattern "test_auth"

# Verbose output
python manage.py test --verbose
```

### Test Structure
```
tests/
├── unit/           # Unit tests
├── integration/    # Integration tests
├── fixtures/       # Test fixtures
└── conftest.py     # Test configuration
```

## Security Features

- **JWT Authentication**: Secure access and refresh tokens
- **Password Hashing**: bcrypt for secure password storage
- **Rate Limiting**: Prevent API abuse
- **Security Headers**: XSS protection, CSRF prevention
- **Input Validation**: Comprehensive Pydantic validation
- **SQL Injection Protection**: SQLAlchemy ORM
- **CORS Configuration**: Configurable cross-origin requests

## Logging

The application uses structured logging with loguru:

- **Console Logging**: Development debugging
- **File Logging**: Persistent log storage with rotation
- **Error Logging**: Dedicated error tracking
- **Security Logging**: Authentication and security events
- **Performance Logging**: Slow operation detection

Log files are stored in the `logs/` directory:
- `app.log` - General application logs
- `errors.log` - Error logs
- `security.log` - Security events
- `performance.log` - Performance issues

## Database Models

### User
- Authentication and user preferences
- Login tracking and account security
- Refresh token management

### CV
- CV metadata and content
- JSON storage for flexible sections
- Template and language support

### File
- File metadata and base64 storage
- Category-based organization
- User and CV associations

### UserActivity
- Comprehensive activity tracking
- Metrics and analytics data
- Audit trail for user actions

## Deployment

### Development
```bash
python manage.py runserver
```

### Production
```bash
# Set production environment variables
export DATABASE_URL_PROD="postgresql+asyncpg://..."
export DEBUG=false

# Run production server
python manage.py runprod --workers 4
```

### Docker (Optional)
```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["python", "manage.py", "runprod"]
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the API documentation at `/docs`
- Review the logs for debugging information
