"""
PDF generation service.

This module provides PDF generation functionality for CVs with
template support, certificate merging, and multilingual capabilities.
"""

import base64
import io
from typing import Optional, List, Dict, Any
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT, TA_JUSTIFY
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from loguru import logger

from app.models.cv import CV
from app.models.file import File as FileModel
from app.core.config import settings
from app.services.pdf_optimization_service import pdf_optimization_service


class PDFService:
    """
    Service for generating PDF documents from CV data.
    
    Supports multiple templates, multilingual content, and certificate merging.
    """
    
    def __init__(self):
        """Initialize PDF service with styles and configurations."""
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """Setup custom paragraph styles for different CV sections."""
        # Header style
        self.styles.add(ParagraphStyle(
            name='CVHeader',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=12,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        ))
        
        # Section header style
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=6,
            spaceBefore=12,
            textColor=colors.darkblue,
            borderWidth=1,
            borderColor=colors.darkblue,
            borderPadding=3
        ))
        
        # Contact info style
        self.styles.add(ParagraphStyle(
            name='ContactInfo',
            parent=self.styles['Normal'],
            fontSize=10,
            alignment=TA_CENTER,
            spaceAfter=6
        ))
        
        # Entry title style
        self.styles.add(ParagraphStyle(
            name='EntryTitle',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceBefore=6,
            spaceAfter=3,
            textColor=colors.black,
            fontName='Helvetica-Bold'
        ))
        
        # Entry details style
        self.styles.add(ParagraphStyle(
            name='EntryDetails',
            parent=self.styles['Normal'],
            fontSize=10,
            spaceAfter=6,
            leftIndent=20
        ))
    
    async def generate_cv_pdf(
        self,
        cv: CV,
        db: AsyncSession,
        template_override: Optional[str] = None,
        include_certificates: bool = True,
        include_cover_letter: bool = True,
        primary_color: Optional[str] = None
    ) -> bytes:
        """
        Generate PDF for a CV with enhanced template support.

        Args:
            cv: CV model instance
            db: Database session
            template_override: Template ID to override CV template
            include_certificates: Whether to include certificate attachments
            include_cover_letter: Whether to include cover letter
            primary_color: Primary color override (hex format)

        Returns:
            bytes: PDF file data
        """
        try:
            # Use optimization service for performance monitoring
            return await pdf_optimization_service.generate_with_monitoring(
                self._generate_pdf_internal,
                cv, db, template_override, include_certificates, include_cover_letter, primary_color
            )

        except Exception as e:
            logger.error(f"PDF generation failed for CV {cv.id}: {e}")
            raise

    async def _generate_pdf_internal(
        self,
        cv: CV,
        db: AsyncSession,
        template_override: Optional[str] = None,
        include_certificates: bool = True,
        include_cover_letter: bool = True,
        primary_color: Optional[str] = None
    ) -> bytes:
        """Internal PDF generation method with optimizations."""
        # Create PDF buffer
        buffer = io.BytesIO()

        # Create document with optimized settings
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
            
        # Determine template to use
        template_id = template_override or cv.template

        # Build content based on template
        story = []

        if template_id == "german":
            story = await self._build_german_template(cv, db, include_certificates, include_cover_letter, primary_color)
        elif template_id == "german":
            story = await self._build_german_ausbildung_template(cv, db)
        elif template_id == "modern":
            story = await self._build_modern_template(cv, db, primary_color)
        elif template_id == "creative":
            story = await self._build_creative_template(cv, db, primary_color)
        else:  # standard template
            story = await self._build_standard_template(cv, db, primary_color)

        # Build PDF
        doc.build(story)

        # Get PDF data
        pdf_data = buffer.getvalue()
        buffer.close()

        # Perform memory cleanup if needed
        pdf_optimization_service.memory_cleanup()

        logger.info(f"PDF generated for CV {cv.id}, template: {template_id}")
        return pdf_data
    
    async def _build_standard_template(self, cv: CV, db: AsyncSession, primary_color: Optional[str] = None) -> List:
        """Build standard CV template."""
        story = []
        
        # Header with personal info
        personal_info = cv.personal_info or {}
        
        # Name header
        name = f"{personal_info.get('firstName', '')} {personal_info.get('lastName', '')}".strip()
        if name:
            story.append(Paragraph(name, self.styles['CVHeader']))
        
        # Contact information
        contact_parts = []
        if personal_info.get('email'):
            contact_parts.append(personal_info['email'])
        if personal_info.get('phone'):
            contact_parts.append(personal_info['phone'])
        if personal_info.get('address'):
            address = f"{personal_info['address']}, {personal_info.get('city', '')}, {personal_info.get('country', '')}".strip(', ')
            contact_parts.append(address)
        
        if contact_parts:
            story.append(Paragraph(" | ".join(contact_parts), self.styles['ContactInfo']))
        
        story.append(Spacer(1, 12))
        
        # Add photo if available
        await self._add_photo_to_story(story, cv, db)
        
        # Work Experience
        if cv.work_experience:
            story.append(Paragraph(self._get_section_title("Work Experience", cv.language), self.styles['SectionHeader']))
            for exp in cv.work_experience:
                story.extend(self._format_work_experience_entry(exp))
        
        # Education
        if cv.education:
            story.append(Paragraph(self._get_section_title("Education", cv.language), self.styles['SectionHeader']))
            for edu in cv.education:
                story.extend(self._format_education_entry(edu))
        
        # Skills
        if cv.skills:
            story.append(Paragraph(self._get_section_title("Skills", cv.language), self.styles['SectionHeader']))
            story.extend(self._format_skills_section(cv.skills, primary_color or "#005A9C"))
        
        # References
        if cv.references:
            story.append(Paragraph(self._get_section_title("References", cv.language), self.styles['SectionHeader']))
            for ref in cv.references:
                story.extend(self._format_reference_entry(ref))
        
        return story
    
    async def _build_modern_template(self, cv: CV, db: AsyncSession, primary_color: Optional[str] = None) -> List:
        """Build modern CV template with enhanced styling and two-column layout."""
        from reportlab.lib.colors import HexColor

        story = []
        color = primary_color or "#10B981"  # Modern green color

        # Modern header with accent color
        personal_info = cv.personal_info or {}

        # Name header with modern styling
        name = f"{personal_info.get('firstName', '')} {personal_info.get('lastName', '')}".strip()
        if name:
            modern_header_style = ParagraphStyle(
                name='ModernHeader',
                parent=self.styles['Normal'],
                fontSize=24,
                spaceAfter=15,
                spaceBefore=10,
                alignment=TA_CENTER,
                textColor=HexColor(color),
                fontName='Helvetica-Bold'
            )
            story.append(Paragraph(name, modern_header_style))

        # Contact bar with modern styling
        contact_parts = []
        if personal_info.get('email'):
            contact_parts.append(f"✉ {personal_info['email']}")
        if personal_info.get('phone'):
            contact_parts.append(f"☎ {personal_info['phone']}")
        if personal_info.get('address'):
            address = f"{personal_info['address']}, {personal_info.get('city', '')}"
            contact_parts.append(f"⌂ {address}")

        if contact_parts:
            contact_style = ParagraphStyle(
                name='ModernContact',
                parent=self.styles['Normal'],
                fontSize=10,
                alignment=TA_CENTER,
                spaceAfter=20,
                textColor=HexColor("#4B5563")
            )
            story.append(Paragraph(" | ".join(contact_parts), contact_style))

        # Add photo with modern styling
        await self._add_modern_photo_to_story(story, cv, db)

        # Professional summary (if available in personal info)
        if personal_info.get('summary'):
            summary_style = ParagraphStyle(
                name='ModernSummary',
                parent=self.styles['Normal'],
                fontSize=11,
                alignment=TA_JUSTIFY,
                spaceAfter=20,
                leftIndent=40,
                rightIndent=40,
                textColor=HexColor("#374151"),
                leading=14
            )
            story.append(Paragraph(personal_info['summary'], summary_style))

        # Work Experience with timeline styling
        if cv.work_experience:
            story.extend(self._build_modern_work_experience(cv.work_experience, color))

        # Education with modern styling
        if cv.education:
            story.extend(self._build_modern_education(cv.education, color))

        # Skills with progress bars
        if cv.skills:
            story.extend(self._build_modern_skills(cv.skills, color))

        # References with card styling
        if cv.references:
            story.extend(self._build_modern_references(cv.references, color))

        return story
    
    async def _build_creative_template(self, cv: CV, db: AsyncSession, primary_color: Optional[str] = None) -> List:
        """Build creative CV template with unique design elements."""
        # For now, use standard template with creative styling
        # This can be enhanced with colors, graphics, etc.
        return await self._build_standard_template(cv, db)
    
    async def _build_german_ausbildung_template(self, cv: CV, db: AsyncSession) -> List:
        """Build German Ausbildung-specific CV template."""
        story = []
        
        # German CV typically starts with personal details
        personal_info = cv.personal_info or {}
        
        # Header
        story.append(Paragraph("Lebenslauf", self.styles['CVHeader']))
        story.append(Spacer(1, 12))
        
        # Personal data table (German style)
        personal_data = [
            ["Name:", f"{personal_info.get('firstName', '')} {personal_info.get('lastName', '')}".strip()],
            ["Geburtsdatum:", personal_info.get('dateOfBirth', '')],
            ["Geburtsort:", personal_info.get('placeOfBirth', '')],
            ["Nationalität:", personal_info.get('nationality', '')],
            ["Familienstand:", personal_info.get('maritalStatus', '')],
            ["Adresse:", f"{personal_info.get('address', '')}, {personal_info.get('postalCode', '')} {personal_info.get('city', '')}".strip(', ')],
            ["Telefon:", personal_info.get('phone', '')],
            ["E-Mail:", personal_info.get('email', '')]
        ]
        
        # Filter out empty rows
        personal_data = [[label, value] for label, value in personal_data if value]
        
        if personal_data:
            table = Table(personal_data, colWidths=[2*inch, 4*inch])
            table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            story.append(table)
            story.append(Spacer(1, 12))
        
        # Continue with standard sections but in German
        if cv.education:
            story.append(Paragraph("Bildungsweg", self.styles['SectionHeader']))
            for edu in cv.education:
                story.extend(self._format_education_entry(edu))
        
        if cv.work_experience:
            story.append(Paragraph("Berufserfahrung", self.styles['SectionHeader']))
            for exp in cv.work_experience:
                story.extend(self._format_work_experience_entry(exp))
        
        if cv.skills:
            story.append(Paragraph("Kenntnisse und Fähigkeiten", self.styles['SectionHeader']))
            story.extend(self._format_skills_section(cv.skills))
        
        return story

    async def _add_photo_to_story(self, story: List, cv: CV, db: AsyncSession):
        """Add photo to PDF story if available."""
        try:
            # Get photo file
            result = await db.execute(
                select(FileModel).where(
                    FileModel.cv_id == cv.id,
                    FileModel.category == "photo"
                )
            )
            photo_file = result.scalar_one_or_none()

            if photo_file and photo_file.file_data:
                # Decode base64 image
                image_data = base64.b64decode(photo_file.file_data)
                image_buffer = io.BytesIO(image_data)

                # Create image
                img = Image(image_buffer, width=1.5*inch, height=2*inch)
                story.append(img)
                story.append(Spacer(1, 12))

        except Exception as e:
            logger.warning(f"Failed to add photo to PDF: {e}")

    def _get_section_title(self, title: str, language: str) -> str:
        """Get localized section title."""
        translations = {
            "en": {
                "Work Experience": "Work Experience",
                "Education": "Education",
                "Skills": "Skills",
                "References": "References"
            },
            "de": {
                "Work Experience": "Berufserfahrung",
                "Education": "Bildungsweg",
                "Skills": "Kenntnisse und Fähigkeiten",
                "References": "Referenzen"
            },
            "ar": {
                "Work Experience": "الخبرة المهنية",
                "Education": "التعليم",
                "Skills": "المهارات",
                "References": "المراجع"
            }
        }

        return translations.get(language, translations["en"]).get(title, title)

    def _format_work_experience_entry(self, exp: Dict[str, Any]) -> List:
        """Format work experience entry."""
        story = []

        # Job title and company
        title_text = f"<b>{exp.get('position', '')}</b> at {exp.get('company', '')}"
        if exp.get('location'):
            title_text += f" ({exp['location']})"
        story.append(Paragraph(title_text, self.styles['EntryTitle']))

        # Dates
        start_date = exp.get('startDate', '')
        end_date = exp.get('endDate', '') if not exp.get('isCurrentlyWorking') else 'Present'
        if start_date:
            date_text = f"{start_date} - {end_date}"
            story.append(Paragraph(date_text, self.styles['EntryDetails']))

        # Description
        if exp.get('description'):
            story.append(Paragraph(exp['description'], self.styles['EntryDetails']))

        story.append(Spacer(1, 6))
        return story

    def _format_education_entry(self, edu: Dict[str, Any]) -> List:
        """Format education entry."""
        story = []

        # Degree and institution
        title_text = f"<b>{edu.get('degree', '')}</b> in {edu.get('fieldOfStudy', '')}"
        story.append(Paragraph(title_text, self.styles['EntryTitle']))

        institution_text = edu.get('institution', '')
        if institution_text:
            story.append(Paragraph(institution_text, self.styles['EntryDetails']))

        # Dates
        start_date = edu.get('startDate', '')
        end_date = edu.get('endDate', '') if not edu.get('isCurrentlyStudying') else 'Present'
        if start_date:
            date_text = f"{start_date} - {end_date}"
            story.append(Paragraph(date_text, self.styles['EntryDetails']))

        # Grade
        if edu.get('grade'):
            story.append(Paragraph(f"Grade: {edu['grade']}", self.styles['EntryDetails']))

        # Description
        if edu.get('description'):
            story.append(Paragraph(edu['description'], self.styles['EntryDetails']))

        story.append(Spacer(1, 6))
        return story

    def _format_skills_section(self, skills: List[Dict[str, Any]], primary_color: str = "#005A9C") -> List:
        """Format skills section with colored level indicators."""
        from reportlab.platypus import Table, TableStyle

        story = []

        # Group skills by category
        skill_groups = {}
        for skill in skills:
            category = skill.get('category', 'other')
            if category not in skill_groups:
                skill_groups[category] = []
            skill_groups[category].append(skill)

        # Format each category
        for category, category_skills in skill_groups.items():
            category_title = category.replace('_', ' ').title()
            story.append(Paragraph(f"<b>{category_title}:</b>", self.styles['EntryTitle']))

            # Create table for skills with level indicators
            skills_data = []
            for skill in category_skills:
                skill_name = skill.get('name', '')
                level = skill.get('level', 'beginner')

                # Convert level to numeric
                level_map = {
                    'beginner': 1, 'intermediate': 2, 'advanced': 3,
                    'expert': 4, 'native': 5
                }
                level_num = level_map.get(level, 1) if isinstance(level, str) else int(level)

                # Create level indicator
                level_indicator = self._create_standard_level_indicator(level_num, primary_color)
                skills_data.append([skill_name, level_indicator])

            if skills_data:
                table = Table(skills_data, colWidths=[2.5*inch, 2*inch])
                table.setStyle(TableStyle([
                    ('ALIGN', (0, 0), (0, -1), 'LEFT'),
                    ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('FONTNAME', (0, 0), (0, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 0), (-1, -1), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
                ]))
                story.append(table)

            story.append(Spacer(1, 6))

        return story

    def _create_standard_level_indicator(self, level: int, primary_color: str) -> Paragraph:
        """Create level indicator with 5 colored circles for standard template."""
        from reportlab.lib.colors import HexColor

        # Create HTML for colored circles
        circles = []
        for i in range(5):
            if i < level:
                # Filled circle with primary color
                circles.append(f'<font color="{primary_color}">●</font>')
            else:
                # Empty circle with light gray
                circles.append('<font color="#CCCCCC">○</font>')

        # Create paragraph with proper styling
        indicator_style = ParagraphStyle(
            name='StandardLevelIndicator',
            parent=self.styles['Normal'],
            fontSize=12,
            fontName='Helvetica'
        )

        return Paragraph(' '.join(circles), indicator_style)

    def _format_reference_entry(self, ref: Dict[str, Any]) -> List:
        """Format reference entry."""
        story = []

        # Name and position
        title_text = f"<b>{ref.get('name', '')}</b>"
        if ref.get('position'):
            title_text += f", {ref['position']}"
        story.append(Paragraph(title_text, self.styles['EntryTitle']))

        # Company
        if ref.get('company'):
            story.append(Paragraph(ref['company'], self.styles['EntryDetails']))

        # Contact info
        contact_parts = []
        if ref.get('email'):
            contact_parts.append(f"Email: {ref['email']}")
        if ref.get('phone'):
            contact_parts.append(f"Phone: {ref['phone']}")

        if contact_parts:
            story.append(Paragraph(" | ".join(contact_parts), self.styles['EntryDetails']))

        story.append(Spacer(1, 6))
        return story

    async def _build_german_template(
        self,
        cv: CV,
        db: AsyncSession,
        include_certificates: bool = True,
        include_cover_letter: bool = True,
        primary_color: Optional[str] = None
    ) -> List:
        """
        Build comprehensive German CV template according to specification.

        Args:
            cv: CV model instance
            db: Database session
            include_certificates: Whether to include certificates
            include_cover_letter: Whether to include cover letter
            primary_color: Primary color override

        Returns:
            List: Story elements for PDF generation
        """
        story = []

        # Use primary color or default
        color = primary_color or "#005A9C"

        # 1. Cover Page (Deckblatt)
        story.extend(await self._build_german_cover_page(cv, db, color))

        # 2. Table of Contents (Inhaltsverzeichnis)
        story.extend(self._build_german_table_of_contents(cv, include_cover_letter, include_certificates, color))

        # 3. CV Main Page (Lebenslauf)
        story.extend(await self._build_german_cv_main_page(cv, db, color))

        # 4. Cover Letter (if exists and included)
        if include_cover_letter and cv.cover_letter:
            story.extend(self._build_german_cover_letter(cv, color))

        # 5. Certificate Attachments (if exist and included)
        if include_certificates:
            story.extend(await self._build_german_certificates(cv, db))

        return story

    async def _build_german_cover_page(self, cv: CV, db: AsyncSession, primary_color: str) -> List:
        """Build German template cover page (Deckblatt)."""
        from reportlab.platypus import PageBreak
        from reportlab.lib.colors import HexColor
        from datetime import datetime

        story = []
        personal_info = cv.personal_info or {}

        # Add vertical space
        story.append(Spacer(1, 80))

        # Add photo if available (circular, 120x120 points)
        await self._add_german_photo_to_story(story, cv, db)

        # Application subject line (from cover letter if available)
        cover_letter_data = self._parse_cover_letter(cv.cover_letter)
        if cover_letter_data and cover_letter_data.get('subject'):
            subject_style = ParagraphStyle(
                name='GermanSubject',
                parent=self.styles['Normal'],
                fontSize=22,
                alignment=TA_CENTER,
                spaceAfter=30,
                spaceBefore=20,
                textColor=HexColor(primary_color),
                fontName='Helvetica-Bold'
            )
            story.append(Paragraph(cover_letter_data['subject'], subject_style))

        # Horizontal divider line
        from reportlab.platypus import HRFlowable
        story.append(HRFlowable(width="100%", thickness=1, color=HexColor("#CCCCCC")))
        story.append(Spacer(1, 30))

        # Applicant's full name
        name = f"{personal_info.get('firstName', '')} {personal_info.get('lastName', '')}".strip()
        if name:
            name_style = ParagraphStyle(
                name='GermanName',
                parent=self.styles['Normal'],
                fontSize=20,
                alignment=TA_CENTER,
                spaceAfter=40,
                textColor=HexColor(primary_color),
                fontName='Helvetica-Bold'
            )
            story.append(Paragraph(name, name_style))

        # Footer with "Bewerbungsunterlagen | [Current Date]"
        current_date = datetime.now().strftime("%d.%m.%Y")
        footer_style = ParagraphStyle(
            name='GermanFooter',
            parent=self.styles['Normal'],
            fontSize=11,
            alignment=TA_CENTER,
            textColor=HexColor("#333333")
        )

        # Add spacer to push footer to bottom
        story.append(Spacer(1, 200))
        story.append(Paragraph(f"Bewerbungsunterlagen | {current_date}", footer_style))

        # Page break
        story.append(PageBreak())

        return story

    def _build_german_table_of_contents(self, cv: CV, include_cover_letter: bool, include_certificates: bool, primary_color: str) -> List:
        """Build German template table of contents (Inhaltsverzeichnis)."""
        from reportlab.platypus import PageBreak
        from reportlab.lib.colors import HexColor

        story = []

        # Title
        toc_title_style = ParagraphStyle(
            name='GermanTOCTitle',
            parent=self.styles['Normal'],
            fontSize=16,
            spaceAfter=20,
            textColor=HexColor(primary_color),
            fontName='Helvetica-Bold'
        )
        story.append(Paragraph("Inhaltsverzeichnis", toc_title_style))

        # TOC items
        toc_item_style = ParagraphStyle(
            name='GermanTOCItem',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=6,
            leftIndent=20
        )

        # 1. Lebenslauf
        story.append(Paragraph("1. Lebenslauf", toc_item_style))

        # Check if sections have data
        has_education = cv.education and len(cv.education) > 0
        has_work_experience = cv.work_experience and len(cv.work_experience) > 0
        has_skills = cv.skills and len(cv.skills) > 0
        has_references = cv.references and len(cv.references) > 0

        if has_education:
            story.append(Paragraph("1.1 Bildung", toc_item_style))
        if has_work_experience:
            story.append(Paragraph("1.2 Berufserfahrung", toc_item_style))
        if has_skills:
            story.append(Paragraph("1.3 Kenntnisse und Fähigkeiten", toc_item_style))
        if has_references:
            story.append(Paragraph("1.4 Referenzen", toc_item_style))

        # 2. Anschreiben (if cover letter exists)
        if include_cover_letter and cv.cover_letter:
            story.append(Paragraph("2. Anschreiben", toc_item_style))

        # 3. Zertifikate (if certificates exist)
        if include_certificates and self._has_certificates(cv):
            story.append(Paragraph("3. Zertifikate", toc_item_style))

        story.append(PageBreak())
        return story

    async def _build_german_cv_main_page(self, cv: CV, db: AsyncSession, primary_color: str) -> List:
        """Build German template main CV page (Lebenslauf)."""
        from reportlab.platypus import PageBreak
        from reportlab.lib.colors import HexColor

        story = []

        # Title
        cv_title_style = ParagraphStyle(
            name='GermanCVTitle',
            parent=self.styles['Normal'],
            fontSize=16,
            spaceAfter=20,
            textColor=HexColor(primary_color),
            fontName='Helvetica-Bold'
        )
        story.append(Paragraph("Lebenslauf", cv_title_style))

        # 1. Personal Information (Persönliche Daten)
        story.extend(self._build_german_personal_info(cv, primary_color))

        # 2. Education (Bildung) - sorted by date descending
        if cv.education and len(cv.education) > 0:
            story.extend(self._build_german_education_section(cv, primary_color))

        # 3. Work Experience (Berufserfahrung) - sorted by date descending
        if cv.work_experience and len(cv.work_experience) > 0:
            story.extend(self._build_german_work_experience_section(cv, primary_color))

        # 4. Skills (Kenntnisse und Fähigkeiten)
        if cv.skills and len(cv.skills) > 0:
            story.extend(self._build_german_skills_section(cv, primary_color))

        # 5. References (Referenzen)
        if cv.references and len(cv.references) > 0:
            story.extend(self._build_german_references_section(cv, primary_color))

        story.append(PageBreak())
        return story

    def _build_german_personal_info(self, cv: CV, primary_color: str) -> List:
        """Build German personal information section (Persönliche Daten)."""
        from reportlab.lib.colors import HexColor

        story = []
        personal_info = cv.personal_info or {}

        # Section header
        section_style = ParagraphStyle(
            name='GermanSectionHeader',
            parent=self.styles['Normal'],
            fontSize=14,
            spaceAfter=12,
            spaceBefore=12,
            textColor=HexColor(primary_color),
            fontName='Helvetica-Bold'
        )
        story.append(Paragraph("Persönliche Daten", section_style))

        # Personal data in two-column format (30% labels, 70% values)
        personal_data = []

        # Name
        name = f"{personal_info.get('firstName', '')} {personal_info.get('lastName', '')}".strip()
        if name:
            personal_data.append(["Name:", name])

        # Address
        address_parts = []
        if personal_info.get('address'):
            address_parts.append(personal_info['address'])
        if personal_info.get('postalCode') and personal_info.get('city'):
            address_parts.append(f"{personal_info['postalCode']} {personal_info['city']}")
        if address_parts:
            personal_data.append(["Adresse:", ", ".join(address_parts)])

        # Phone
        if personal_info.get('phone'):
            personal_data.append(["Telefon:", personal_info['phone']])

        # Email
        if personal_info.get('email'):
            personal_data.append(["E-Mail:", personal_info['email']])

        # Optional fields
        if personal_info.get('dateOfBirth'):
            personal_data.append(["Geburtsdatum:", personal_info['dateOfBirth']])

        if personal_info.get('nationality'):
            personal_data.append(["Nationalität:", personal_info['nationality']])

        # Create table
        if personal_data:
            table = Table(personal_data, colWidths=[2*inch, 4*inch])
            table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 11),
                ('TEXTCOLOR', (0, 0), (0, -1), HexColor("#444444")),
                ('TEXTCOLOR', (1, 0), (1, -1), HexColor("#333333")),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
            ]))
            story.append(table)
            story.append(Spacer(1, 12))

        return story

    def _build_german_education_section(self, cv: CV, primary_color: str) -> List:
        """Build German education section (Bildung) with proper sorting."""
        from reportlab.lib.colors import HexColor

        story = []

        # Section header
        section_style = ParagraphStyle(
            name='GermanSectionHeader',
            parent=self.styles['Normal'],
            fontSize=14,
            spaceAfter=12,
            spaceBefore=12,
            textColor=HexColor(primary_color),
            fontName='Helvetica-Bold'
        )
        story.append(Paragraph("Bildung", section_style))

        # Sort education entries (current first, then by end date descending)
        education_entries = self._sort_entries_chronologically(cv.education)

        for edu in education_entries:
            story.extend(self._format_german_education_entry(edu, primary_color))

        return story

    def _format_german_education_entry(self, edu: Dict[str, Any], primary_color: str) -> List:
        """Format German education entry."""
        from reportlab.lib.colors import HexColor

        story = []

        # Date formatting
        date_text = self._format_german_date_range(
            edu.get('startDate'),
            edu.get('endDate'),
            edu.get('isCurrentlyStudying', False)
        )

        # Create two-column layout (25% dates, 75% details)
        entry_data = []

        # Degree (bold, primary color) - Create Paragraph for proper rendering
        degree = edu.get('degree', '')
        if degree:
            degree_style = ParagraphStyle(
                name='GermanDegree',
                parent=self.styles['Normal'],
                fontSize=11,
                textColor=HexColor(primary_color),
                fontName='Helvetica-Bold'
            )
            degree_para = Paragraph(degree, degree_style)
            entry_data.append([date_text, degree_para])

        # Institution
        institution = edu.get('institution', '')
        if institution:
            institution_style = ParagraphStyle(
                name='GermanInstitution',
                parent=self.styles['Normal'],
                fontSize=11,
                fontName='Helvetica'
            )
            institution_para = Paragraph(institution, institution_style)
            if not degree:  # If no degree, put institution in first row
                entry_data.append([date_text, institution_para])
            else:
                entry_data.append(['', institution_para])

        # Description (smaller font, secondary color)
        description = edu.get('description', '')
        if description:
            desc_style = ParagraphStyle(
                name='GermanEduDesc',
                parent=self.styles['Normal'],
                fontSize=10,
                textColor=HexColor("#444444")
            )
            desc_para = Paragraph(description, desc_style)
            entry_data.append(['', desc_para])

        # Certificate indicator
        if edu.get('certificates') and len(edu.get('certificates', [])) > 0:
            cert_style = ParagraphStyle(
                name='GermanCertIndicator',
                parent=self.styles['Normal'],
                fontSize=10,
                textColor=HexColor("#444444")
            )
            cert_para = Paragraph('Zertifikat: verfügbar', cert_style)
            entry_data.append(['', cert_para])

        if entry_data:
            table = Table(entry_data, colWidths=[1.5*inch, 4.5*inch])
            table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 11),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
            ]))
            story.append(table)
            story.append(Spacer(1, 6))

        return story

    def _sort_entries_chronologically(self, entries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Sort entries chronologically (current first, then by end date descending)."""
        if not entries:
            return []

        def sort_key(entry):
            # Current entries first
            is_current = entry.get('isCurrentlyStudying', False) or entry.get('isCurrentlyWorking', False)
            if is_current:
                return (0, '9999-12-31')  # Current entries get highest priority

            # Then by end date descending
            end_date = entry.get('endDate', '')
            if not end_date:
                return (1, '0000-01-01')  # Entries without end date go last

            return (1, end_date)

        return sorted(entries, key=sort_key, reverse=True)

    def _format_german_date_range(self, start_date: str, end_date: str, is_current: bool) -> str:
        """Format date range in German format (MM/YYYY - MM/YYYY or MM/YYYY - Heute)."""
        if not start_date:
            return ""

        # Format start date
        try:
            from datetime import datetime
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            start_formatted = start_dt.strftime("%m/%Y")
        except:
            start_formatted = start_date

        # Format end date
        if is_current:
            end_formatted = "Heute"
        elif end_date:
            try:
                end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                end_formatted = end_dt.strftime("%m/%Y")
            except:
                end_formatted = end_date
        else:
            end_formatted = "Heute"

        return f"{start_formatted} - {end_formatted}"

    def _build_german_work_experience_section(self, cv: CV, primary_color: str) -> List:
        """Build German work experience section (Berufserfahrung)."""
        from reportlab.lib.colors import HexColor

        story = []

        # Section header
        section_style = ParagraphStyle(
            name='GermanSectionHeader',
            parent=self.styles['Normal'],
            fontSize=14,
            spaceAfter=12,
            spaceBefore=12,
            textColor=HexColor(primary_color),
            fontName='Helvetica-Bold'
        )
        story.append(Paragraph("Berufserfahrung", section_style))

        # Sort work experience entries
        work_entries = self._sort_entries_chronologically(cv.work_experience)

        for work in work_entries:
            story.extend(self._format_german_work_entry(work, primary_color))

        return story

    def _format_german_work_entry(self, work: Dict[str, Any], primary_color: str) -> List:
        """Format German work experience entry."""
        from reportlab.lib.colors import HexColor

        story = []

        # Date formatting
        date_text = self._format_german_date_range(
            work.get('startDate'),
            work.get('endDate'),
            work.get('isCurrentlyWorking', False)
        )

        # Create two-column layout
        entry_data = []

        # Position (bold, primary color) - Create Paragraph for proper rendering
        position = work.get('position', '')
        if position:
            position_style = ParagraphStyle(
                name='GermanPosition',
                parent=self.styles['Normal'],
                fontSize=11,
                textColor=HexColor(primary_color),
                fontName='Helvetica-Bold'
            )
            position_para = Paragraph(position, position_style)
            entry_data.append([date_text, position_para])

        # Company
        company = work.get('company', '')
        if company:
            company_style = ParagraphStyle(
                name='GermanCompany',
                parent=self.styles['Normal'],
                fontSize=11,
                fontName='Helvetica'
            )
            company_para = Paragraph(company, company_style)
            if not position:
                entry_data.append([date_text, company_para])
            else:
                entry_data.append(['', company_para])

        # Description
        description = work.get('description', '')
        if description:
            desc_style = ParagraphStyle(
                name='GermanWorkDesc',
                parent=self.styles['Normal'],
                fontSize=10,
                textColor=HexColor("#444444")
            )
            desc_para = Paragraph(description, desc_style)
            entry_data.append(['', desc_para])

        if entry_data:
            table = Table(entry_data, colWidths=[1.5*inch, 4.5*inch])
            table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 11),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
            ]))
            story.append(table)
            story.append(Spacer(1, 6))

        return story

    def _build_german_skills_section(self, cv: CV, primary_color: str) -> List:
        """Build German skills section (Kenntnisse und Fähigkeiten) with translations."""
        from reportlab.lib.colors import HexColor

        story = []

        # Section header
        section_style = ParagraphStyle(
            name='GermanSectionHeader',
            parent=self.styles['Normal'],
            fontSize=14,
            spaceAfter=12,
            spaceBefore=12,
            textColor=HexColor(primary_color),
            fontName='Helvetica-Bold'
        )
        story.append(Paragraph("Kenntnisse und Fähigkeiten", section_style))

        # Group skills by category and translate
        skills_by_category = self._group_and_translate_german_skills(cv.skills)

        # Display in order: Technical, Language, Soft Skills
        category_order = ["Technisch", "Sprache", "Soft Skills"]

        for category in category_order:
            if category in skills_by_category:
                story.extend(self._format_german_skills_category(category, skills_by_category[category], primary_color))

        return story

    def _group_and_translate_german_skills(self, skills: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Group skills by category and translate to German."""
        # Translation maps from specification
        category_translations = {
            "technical": "Technisch",
            "language": "Sprache",
            "soft": "Soft Skills"
        }

        technical_translations = {
            "javascript": "JavaScript", "typescript": "TypeScript", "react": "React",
            "angular": "Angular", "vue": "Vue.js", "node": "Node.js", "python": "Python",
            "java": "Java", "csharp": "C#", "cpp": "C++", "php": "PHP", "sql": "SQL",
            "mongodb": "MongoDB", "postgresql": "PostgreSQL", "mysql": "MySQL"
        }

        language_translations = {
            "english": "Englisch", "german": "Deutsch", "arabic": "Arabisch",
            "french": "Französisch", "spanish": "Spanisch", "italian": "Italienisch",
            "russian": "Russisch", "chinese": "Chinesisch", "japanese": "Japanisch"
        }

        soft_skills_translations = {
            "teamwork": "Teamarbeit", "communication": "Kommunikation",
            "leadership": "Führungsqualitäten", "problemsolving": "Problemlösung",
            "creativity": "Kreativität", "adaptability": "Anpassungsfähigkeit",
            "timemanagement": "Zeitmanagement"
        }

        grouped_skills = {}

        for skill in skills:
            category = skill.get('category', 'technical')
            german_category = category_translations.get(category, category)

            if german_category not in grouped_skills:
                grouped_skills[german_category] = []

            # Translate skill name
            skill_name = skill.get('name', '').lower()
            translated_name = skill.get('name', '')  # Default to original

            if category == 'technical':
                translated_name = technical_translations.get(skill_name, skill.get('name', ''))
            elif category == 'language':
                translated_name = language_translations.get(skill_name, skill.get('name', ''))
            elif category == 'soft':
                translated_name = soft_skills_translations.get(skill_name, skill.get('name', ''))

            translated_skill = skill.copy()
            translated_skill['name'] = translated_name
            grouped_skills[german_category].append(translated_skill)

        return grouped_skills

    def _format_german_skills_category(self, category: str, skills: List[Dict[str, Any]], primary_color: str) -> List:
        """Format German skills category with level indicators."""
        from reportlab.lib.colors import HexColor
        from reportlab.platypus import Table, TableStyle

        story = []

        # Category header (12pt, bold, primary color, with underline)
        category_style = ParagraphStyle(
            name='GermanSkillsCategory',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=8,
            spaceBefore=8,
            textColor=HexColor(primary_color),
            fontName='Helvetica-Bold'
        )

        # Add underline using HTML
        category_html = f'<u>{category}</u>'
        story.append(Paragraph(category_html, category_style))

        # Create skills table with level indicators
        skills_data = []

        for skill in skills:
            skill_name = skill.get('name', '')
            level = skill.get('level', 'beginner')

            # Convert level to numeric (1-5)
            level_map = {
                'beginner': 1, 'intermediate': 2, 'advanced': 3,
                'expert': 4, 'native': 5
            }
            level_num = level_map.get(level, 1) if isinstance(level, str) else int(level)

            # Create level indicator (5 dots)
            level_dots = self._create_german_level_indicator(level_num, primary_color)

            skills_data.append([skill_name, level_dots])

        if skills_data:
            # Create table with flexible layout
            table = Table(skills_data, colWidths=[3*inch, 2*inch])
            table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (0, -1), 'LEFT'),
                ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 11),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
                ('TOPPADDING', (0, 0), (-1, -1), 4),
            ]))
            story.append(table)
            story.append(Spacer(1, 12))

        return story

    def _create_german_level_indicator(self, level: int, primary_color: str) -> Paragraph:
        """Create level indicator with 5 colored circles."""
        from reportlab.lib.colors import HexColor

        # Create HTML for colored circles
        circles = []
        for i in range(5):
            if i < level:
                # Filled circle with primary color
                circles.append(f'<font color="{primary_color}">●</font>')
            else:
                # Empty circle with light gray
                circles.append('<font color="#CCCCCC">○</font>')

        # Create paragraph with proper styling
        indicator_style = ParagraphStyle(
            name='GermanLevelIndicator',
            parent=self.styles['Normal'],
            fontSize=12,
            fontName='Helvetica'
        )

        return Paragraph(' '.join(circles), indicator_style)

    def _build_german_references_section(self, cv: CV, primary_color: str) -> List:
        """Build German references section (Referenzen)."""
        from reportlab.lib.colors import HexColor

        story = []

        # Section header
        section_style = ParagraphStyle(
            name='GermanSectionHeader',
            parent=self.styles['Normal'],
            fontSize=14,
            spaceAfter=12,
            spaceBefore=12,
            textColor=HexColor(primary_color),
            fontName='Helvetica-Bold'
        )
        story.append(Paragraph("Referenzen", section_style))

        for ref in cv.references:
            story.extend(self._format_german_reference_entry(ref, primary_color))

        return story

    def _format_german_reference_entry(self, ref: Dict[str, Any], primary_color: str) -> List:
        """Format German reference entry."""
        from reportlab.lib.colors import HexColor

        story = []

        # Name (bold, primary color)
        name = ref.get('name', '')
        if name:
            name_style = ParagraphStyle(
                name='GermanRefName',
                parent=self.styles['Normal'],
                fontSize=11,
                spaceAfter=3,
                textColor=HexColor(primary_color),
                fontName='Helvetica-Bold'
            )
            story.append(Paragraph(name, name_style))

        # Position
        position = ref.get('position', '')
        if position:
            position_style = ParagraphStyle(
                name='GermanRefPosition',
                parent=self.styles['Normal'],
                fontSize=11,
                spaceAfter=3
            )
            story.append(Paragraph(position, position_style))

        # Contact info (company | email | phone)
        contact_parts = []
        if ref.get('company'):
            contact_parts.append(ref['company'])
        if ref.get('email'):
            contact_parts.append(ref['email'])
        if ref.get('phone'):
            contact_parts.append(ref['phone'])

        if contact_parts:
            contact_style = ParagraphStyle(
                name='GermanRefContact',
                parent=self.styles['Normal'],
                fontSize=10,
                spaceAfter=8,
                textColor=HexColor("#444444")
            )
            story.append(Paragraph(" | ".join(contact_parts), contact_style))

        story.append(Spacer(1, 6))
        return story

    def _build_german_cover_letter(self, cv: CV, primary_color: str) -> List:
        """Build German cover letter (Anschreiben)."""
        from reportlab.platypus import PageBreak
        from reportlab.lib.colors import HexColor
        from datetime import datetime

        story = []

        # Parse cover letter data
        cover_letter_data = self._parse_cover_letter(cv.cover_letter)
        if not cover_letter_data:
            return story

        personal_info = cv.personal_info or {}

        # Sender info (top-left, 8pt)
        sender_style = ParagraphStyle(
            name='GermanSenderInfo',
            parent=self.styles['Normal'],
            fontSize=8,
            spaceAfter=20,
            textColor=HexColor("#444444")
        )

        sender_info = []
        name = f"{personal_info.get('firstName', '')} {personal_info.get('lastName', '')}".strip()
        if name:
            sender_info.append(name)
        if personal_info.get('address'):
            sender_info.append(personal_info['address'])
        if personal_info.get('postalCode') and personal_info.get('city'):
            sender_info.append(f"{personal_info['postalCode']} {personal_info['city']}")
        if personal_info.get('phone'):
            sender_info.append(personal_info['phone'])
        if personal_info.get('email'):
            sender_info.append(personal_info['email'])

        if sender_info:
            story.append(Paragraph("<br/>".join(sender_info), sender_style))

        # Recipient info (left-aligned, 10pt)
        if cover_letter_data.get('recipientName') or cover_letter_data.get('recipientAddress'):
            recipient_style = ParagraphStyle(
                name='GermanRecipientInfo',
                parent=self.styles['Normal'],
                fontSize=10,
                spaceAfter=20
            )

            recipient_info = []
            if cover_letter_data.get('recipientName'):
                recipient_info.append(cover_letter_data['recipientName'])
            if cover_letter_data.get('recipientAddress'):
                recipient_info.append(cover_letter_data['recipientAddress'])

            if recipient_info:
                story.append(Paragraph("<br/>".join(recipient_info), recipient_style))

        # Date (right-aligned, 10pt)
        date_style = ParagraphStyle(
            name='GermanDate',
            parent=self.styles['Normal'],
            fontSize=10,
            alignment=TA_RIGHT,
            spaceAfter=30
        )
        current_date = datetime.now().strftime("%d.%m.%Y")
        story.append(Paragraph(current_date, date_style))

        # Subject (bold, 12pt, primary color)
        if cover_letter_data.get('subject'):
            subject_style = ParagraphStyle(
                name='GermanSubject',
                parent=self.styles['Normal'],
                fontSize=12,
                spaceAfter=20,
                textColor=HexColor(primary_color),
                fontName='Helvetica-Bold'
            )
            story.append(Paragraph(cover_letter_data['subject'], subject_style))

        # Salutation
        if cover_letter_data.get('salutation'):
            salutation_style = ParagraphStyle(
                name='GermanSalutation',
                parent=self.styles['Normal'],
                fontSize=10,
                spaceAfter=15
            )
            story.append(Paragraph(cover_letter_data['salutation'], salutation_style))

        # Content (justified text, 10pt, line height 1.5)
        if cover_letter_data.get('content'):
            content_style = ParagraphStyle(
                name='GermanContent',
                parent=self.styles['Normal'],
                fontSize=10,
                alignment=TA_JUSTIFY,
                spaceAfter=20,
                leading=15  # 1.5 line height
            )
            story.append(Paragraph(cover_letter_data['content'], content_style))

        # Closing
        if cover_letter_data.get('closing'):
            closing_style = ParagraphStyle(
                name='GermanClosing',
                parent=self.styles['Normal'],
                fontSize=10,
                spaceAfter=30
            )
            story.append(Paragraph(cover_letter_data['closing'], closing_style))

        # Signature space (30pt margin)
        story.append(Spacer(1, 30))

        story.append(PageBreak())
        return story

    async def _build_german_certificates(self, cv: CV, db: AsyncSession) -> List:
        """Build German certificate attachments section."""
        from reportlab.platypus import PageBreak
        from reportlab.lib.colors import HexColor

        story = []

        # Check if there are certificates to include
        if not self._has_certificates(cv):
            return story

        # Section title
        cert_title_style = ParagraphStyle(
            name='GermanCertTitle',
            parent=self.styles['Normal'],
            fontSize=16,
            spaceAfter=20,
            textColor=HexColor("#005A9C"),
            fontName='Helvetica-Bold'
        )
        story.append(Paragraph("Zertifikate", cert_title_style))

        # Get certificate files from education entries
        certificates_added = False

        logger.debug(f"Processing certificates for CV {cv.id}")

        for edu in cv.education or []:
            certificates = edu.get('certificates', [])
            logger.debug(f"Education entry {edu.get('institution', 'Unknown')} has certificates: {certificates}")

            if certificates:
                # Add education entry title
                edu_title = edu.get('degree', '') or edu.get('institution', '')
                if edu_title:
                    edu_style = ParagraphStyle(
                        name='GermanCertEduTitle',
                        parent=self.styles['Normal'],
                        fontSize=12,
                        spaceAfter=10,
                        fontName='Helvetica-Bold'
                    )
                    story.append(Paragraph(edu_title, edu_style))

                # Process each certificate
                for cert_id in certificates:
                    logger.debug(f"Processing certificate ID: {cert_id}")
                    cert_data = await self._get_certificate_data(cert_id, db)
                    if cert_data:
                        logger.debug(f"Found certificate data: {cert_data['name']}")
                        story.extend(await self._add_certificate_to_story(cert_data))
                        certificates_added = True
                    else:
                        logger.warning(f"Certificate data not found for ID: {cert_id}")

        # If no certificates were added, remove the title
        if not certificates_added:
            return []

        return story

    def _has_certificates(self, cv: CV) -> bool:
        """Check if CV has any certificates."""
        for edu in cv.education or []:
            if edu.get('certificates') and len(edu.get('certificates', [])) > 0:
                return True
        return False

    async def _get_certificate_data(self, cert_id: str, db: AsyncSession) -> Optional[Dict[str, Any]]:
        """Get certificate data from database."""
        try:
            # Try to get from File model first
            result = await db.execute(
                select(FileModel).where(FileModel.id == cert_id)
            )
            file_record = result.scalar_one_or_none()

            if file_record:
                # Handle both binary and base64 data for backward compatibility
                file_data = file_record.file_data
                if isinstance(file_data, str):
                    # Legacy base64 data - convert to bytes
                    import base64
                    file_data = base64.b64decode(file_data)

                return {
                    'id': file_record.id,
                    'name': file_record.name,
                    'type': file_record.type,
                    'data': file_data
                }

            # Try Certificate model as fallback
            from app.models.certificate import Certificate
            result = await db.execute(
                select(Certificate).where(Certificate.id == int(cert_id))
            )
            cert_record = result.scalar_one_or_none()

            if cert_record:
                return {
                    'id': str(cert_record.id),
                    'name': cert_record.file_name,
                    'type': cert_record.file_type,
                    'data': cert_record.file_data
                }

        except Exception as e:
            logger.error(f"Error retrieving certificate {cert_id}: {e}")

        return None

    async def _add_certificate_to_story(self, cert_data: Dict[str, Any]) -> List:
        """Add certificate to PDF story."""
        from reportlab.lib.colors import HexColor

        story = []

        try:
            if cert_data['type'] == 'application/pdf':
                # For PDF certificates, we would need to merge them
                # For now, add a placeholder
                placeholder_style = ParagraphStyle(
                    name='CertPlaceholder',
                    parent=self.styles['Normal'],
                    fontSize=10,
                    spaceAfter=10,
                    textColor=HexColor("#666666")
                )
                story.append(Paragraph(f"Zertifikat: {cert_data['name']} (PDF)", placeholder_style))

            elif cert_data['type'].startswith('image/'):
                # For image certificates, add them as images
                try:
                    import base64
                    from reportlab.platypus import Image as ReportLabImage
                    from io import BytesIO

                    image_data = base64.b64decode(cert_data['data'])
                    image_buffer = BytesIO(image_data)

                    # Create image with reasonable size
                    img = ReportLabImage(image_buffer, width=400, height=300)
                    story.append(img)
                    story.append(Spacer(1, 10))

                except Exception as e:
                    logger.error(f"Error adding image certificate: {e}")
                    # Add placeholder if image fails
                    story.append(Paragraph(f"Zertifikat: {cert_data['name']} (Bild)", placeholder_style))

        except Exception as e:
            logger.error(f"Error processing certificate: {e}")

        return story

    def _parse_cover_letter(self, cover_letter: Optional[str]) -> Optional[Dict[str, str]]:
        """Parse cover letter content with new comprehensive structure."""
        if not cover_letter:
            return None

        try:
            import json
            # Try to parse as JSON first
            if isinstance(cover_letter, str) and cover_letter.strip().startswith('{'):
                data = json.loads(cover_letter)

                # Ensure all required fields are present with defaults
                return {
                    'recipientName': data.get('recipientName', ''),
                    'company': data.get('company', ''),
                    'address': data.get('address', ''),
                    'postalCode': data.get('postalCode', ''),
                    'city': data.get('city', ''),
                    'country': data.get('country', ''),
                    'email': data.get('email', ''),
                    'phone': data.get('phone', ''),
                    'otherInformation': data.get('otherInformation', ''),
                    'subject': data.get('subject', 'Bewerbung'),
                    'date': data.get('date', ''),
                    'content': data.get('content', ''),
                    'signatureFileId': data.get('signatureFileId', ''),
                    # Legacy fields for backward compatibility
                    'salutation': 'Sehr geehrte Damen und Herren,' if not data.get('recipientName') else f"Sehr geehrte/r {data.get('recipientName')},",
                    'closing': 'Mit freundlichen Grüßen',
                    'recipientAddress': f"{data.get('address', '')}\n{data.get('postalCode', '')} {data.get('city', '')}\n{data.get('country', '')}".strip()
                }
            elif isinstance(cover_letter, dict):
                return cover_letter
            else:
                # Treat as plain text content
                return {
                    'content': str(cover_letter),
                    'subject': 'Bewerbung',
                    'salutation': 'Sehr geehrte Damen und Herren,',
                    'closing': 'Mit freundlichen Grüßen'
                }
        except Exception as e:
            logger.error(f"Error parsing cover letter: {e}")
            # Fallback to plain text
            return {
                'content': str(cover_letter),
                'subject': 'Bewerbung',
                'salutation': 'Sehr geehrte Damen und Herren,',
                'closing': 'Mit freundlichen Grüßen'
            }

    async def _add_german_photo_to_story(self, story: List, cv: CV, db: AsyncSession):
        """Add photo to German template story (circular, 120x120 points)."""
        personal_info = cv.personal_info or {}
        photo_id = personal_info.get('photoUrl')

        if not photo_id:
            logger.debug("No photo ID found in personal info")
            return

        try:
            # Handle both full file ID and just ID formats
            if photo_id.startswith('file_'):
                file_id = photo_id
            else:
                file_id = photo_id

            logger.debug(f"Looking for photo file with ID: {file_id}")

            # Get photo file from database using file ID
            result = await db.execute(
                select(FileModel).where(FileModel.id == file_id)
            )
            photo_file = result.scalar_one_or_none()

            if not photo_file:
                logger.warning(f"Photo file not found with ID: {file_id}")
                return

            if photo_file and photo_file.file_data:
                from reportlab.platypus import Image as ReportLabImage
                from io import BytesIO
                from PIL import Image, ImageDraw

                # Handle both binary and base64 data for backward compatibility
                if isinstance(photo_file.file_data, bytes):
                    image_data = photo_file.file_data
                else:
                    # Legacy base64 data
                    import base64
                    image_data = base64.b64decode(photo_file.file_data)

                optimized_image_data = pdf_optimization_service.optimize_image(image_data, 120, 120)

                # Create circular image
                pil_image = Image.open(BytesIO(optimized_image_data))

                # Resize to 120x120
                pil_image = pil_image.resize((120, 120), Image.Resampling.LANCZOS)

                # Create circular mask
                mask = Image.new('L', (120, 120), 0)
                draw = ImageDraw.Draw(mask)
                draw.ellipse((0, 0, 120, 120), fill=255)

                # Apply mask
                pil_image.putalpha(mask)

                # Convert back to bytes
                img_buffer = BytesIO()
                pil_image.save(img_buffer, format='PNG')
                img_buffer.seek(0)

                # Add to story
                img = ReportLabImage(img_buffer, width=120, height=120)
                story.append(img)
                story.append(Spacer(1, 20))

        except Exception as e:
            logger.error(f"Error adding photo to German template: {e}")
            # Continue without photo

    async def _add_modern_photo_to_story(self, story: List, cv: CV, db: AsyncSession):
        """Add photo to modern template with rounded corners."""
        personal_info = cv.personal_info or {}
        photo_id = personal_info.get('photoUrl')

        if not photo_id:
            return

        try:
            # Get photo file from database using file ID
            result = await db.execute(
                select(FileModel).where(FileModel.id == photo_id)
            )
            photo_file = result.scalar_one_or_none()

            if photo_file and photo_file.file_data:
                import base64
                from reportlab.platypus import Image as ReportLabImage
                from io import BytesIO

                # Decode and resize image
                image_data = base64.b64decode(photo_file.file_data)
                img_buffer = BytesIO(image_data)

                # Add to story with modern styling
                img = ReportLabImage(img_buffer, width=100, height=100)
                story.append(img)
                story.append(Spacer(1, 15))

        except Exception as e:
            logger.error(f"Error adding photo to modern template: {e}")

    def _build_modern_work_experience(self, work_experience: List[Dict[str, Any]], primary_color: str) -> List:
        """Build modern work experience section with timeline styling."""
        from reportlab.lib.colors import HexColor

        story = []

        # Section header with modern styling
        section_style = ParagraphStyle(
            name='ModernSectionHeader',
            parent=self.styles['Normal'],
            fontSize=16,
            spaceAfter=15,
            spaceBefore=20,
            textColor=HexColor(primary_color),
            fontName='Helvetica-Bold'
        )
        story.append(Paragraph("Professional Experience", section_style))

        # Sort entries chronologically
        sorted_work = self._sort_entries_chronologically(work_experience)

        for work in sorted_work:
            story.extend(self._format_modern_work_entry(work, primary_color))

        return story

    def _format_modern_work_entry(self, work: Dict[str, Any], primary_color: str) -> List:
        """Format modern work experience entry with timeline styling."""
        from reportlab.lib.colors import HexColor

        story = []

        # Position and company in one line
        position = work.get('position', '')
        company = work.get('company', '')

        if position and company:
            title_text = f"<b>{position}</b> at {company}"
        elif position:
            title_text = f"<b>{position}</b>"
        elif company:
            title_text = company
        else:
            title_text = ""

        if title_text:
            title_style = ParagraphStyle(
                name='ModernWorkTitle',
                parent=self.styles['Normal'],
                fontSize=12,
                spaceAfter=4,
                textColor=HexColor(primary_color)
            )
            story.append(Paragraph(title_text, title_style))

        # Date range
        date_text = self._format_date_range(
            work.get('startDate'),
            work.get('endDate'),
            work.get('isCurrentlyWorking', False)
        )

        if date_text:
            date_style = ParagraphStyle(
                name='ModernWorkDate',
                parent=self.styles['Normal'],
                fontSize=10,
                spaceAfter=8,
                textColor=HexColor("#6B7280"),
                fontName='Helvetica-Oblique'
            )
            story.append(Paragraph(date_text, date_style))

        # Description with bullet points
        description = work.get('description', '')
        if description:
            desc_style = ParagraphStyle(
                name='ModernWorkDesc',
                parent=self.styles['Normal'],
                fontSize=10,
                spaceAfter=15,
                leftIndent=20,
                textColor=HexColor("#374151"),
                leading=13
            )
            # Add bullet point
            story.append(Paragraph(f"• {description}", desc_style))

        return story

    def _build_modern_education(self, education: List[Dict[str, Any]], primary_color: str) -> List:
        """Build modern education section."""
        from reportlab.lib.colors import HexColor

        story = []

        # Section header
        section_style = ParagraphStyle(
            name='ModernSectionHeader',
            parent=self.styles['Normal'],
            fontSize=16,
            spaceAfter=15,
            spaceBefore=20,
            textColor=HexColor(primary_color),
            fontName='Helvetica-Bold'
        )
        story.append(Paragraph("Education", section_style))

        # Sort entries chronologically
        sorted_education = self._sort_entries_chronologically(education)

        for edu in sorted_education:
            story.extend(self._format_modern_education_entry(edu, primary_color))

        return story

    def _format_modern_education_entry(self, edu: Dict[str, Any], primary_color: str) -> List:
        """Format modern education entry."""
        from reportlab.lib.colors import HexColor

        story = []

        # Degree and institution
        degree = edu.get('degree', '')
        institution = edu.get('institution', '')

        if degree and institution:
            title_text = f"<b>{degree}</b> from {institution}"
        elif degree:
            title_text = f"<b>{degree}</b>"
        elif institution:
            title_text = institution
        else:
            title_text = ""

        if title_text:
            title_style = ParagraphStyle(
                name='ModernEduTitle',
                parent=self.styles['Normal'],
                fontSize=12,
                spaceAfter=4,
                textColor=HexColor(primary_color)
            )
            story.append(Paragraph(title_text, title_style))

        # Date range
        date_text = self._format_date_range(
            edu.get('startDate'),
            edu.get('endDate'),
            edu.get('isCurrentlyStudying', False)
        )

        if date_text:
            date_style = ParagraphStyle(
                name='ModernEduDate',
                parent=self.styles['Normal'],
                fontSize=10,
                spaceAfter=15,
                textColor=HexColor("#6B7280"),
                fontName='Helvetica-Oblique'
            )
            story.append(Paragraph(date_text, date_style))

        return story

    def _build_modern_skills(self, skills: List[Dict[str, Any]], primary_color: str) -> List:
        """Build modern skills section with progress bars."""
        from reportlab.lib.colors import HexColor

        story = []

        # Section header
        section_style = ParagraphStyle(
            name='ModernSectionHeader',
            parent=self.styles['Normal'],
            fontSize=16,
            spaceAfter=15,
            spaceBefore=20,
            textColor=HexColor(primary_color),
            fontName='Helvetica-Bold'
        )
        story.append(Paragraph("Skills & Expertise", section_style))

        # Group skills by category
        skills_by_category = {}
        for skill in skills:
            category = skill.get('category', 'technical')
            if category not in skills_by_category:
                skills_by_category[category] = []
            skills_by_category[category].append(skill)

        # Display each category
        for category, category_skills in skills_by_category.items():
            category_title = category.replace('_', ' ').title()

            category_style = ParagraphStyle(
                name='ModernSkillCategory',
                parent=self.styles['Normal'],
                fontSize=12,
                spaceAfter=8,
                spaceBefore=8,
                textColor=HexColor(primary_color),
                fontName='Helvetica-Bold'
            )
            story.append(Paragraph(category_title, category_style))

            # Create skills table with progress bars
            skills_data = []
            for skill in category_skills:
                skill_name = skill.get('name', '')
                level = skill.get('level', 'beginner')

                # Convert level to numeric
                level_map = {
                    'beginner': 1, 'intermediate': 2, 'advanced': 3,
                    'expert': 4, 'native': 5
                }
                level_num = level_map.get(level, 1) if isinstance(level, str) else int(level)

                # Create progress bar representation
                progress_bar = self._create_modern_progress_bar(level_num, primary_color)
                skills_data.append([skill_name, progress_bar])

            if skills_data:
                table = Table(skills_data, colWidths=[2.5*inch, 2*inch])
                table.setStyle(TableStyle([
                    ('ALIGN', (0, 0), (0, -1), 'LEFT'),
                    ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('FONTNAME', (0, 0), (0, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 0), (-1, -1), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
                ]))
                story.append(table)
                story.append(Spacer(1, 10))

        return story

    def _create_modern_progress_bar(self, level: int, primary_color: str) -> Paragraph:
        """Create modern level indicator with colored circles."""
        from reportlab.lib.colors import HexColor

        # Create HTML for colored circles (same as German template)
        circles = []
        for i in range(5):
            if i < level:
                # Filled circle with primary color
                circles.append(f'<font color="{primary_color}">●</font>')
            else:
                # Empty circle with light gray
                circles.append('<font color="#CCCCCC">○</font>')

        # Create paragraph with proper styling
        indicator_style = ParagraphStyle(
            name='ModernLevelIndicator',
            parent=self.styles['Normal'],
            fontSize=12,
            fontName='Helvetica'
        )

        return Paragraph(' '.join(circles), indicator_style)

    def _build_modern_references(self, references: List[Dict[str, Any]], primary_color: str) -> List:
        """Build modern references section with card styling."""
        from reportlab.lib.colors import HexColor

        story = []

        # Section header
        section_style = ParagraphStyle(
            name='ModernSectionHeader',
            parent=self.styles['Normal'],
            fontSize=16,
            spaceAfter=15,
            spaceBefore=20,
            textColor=HexColor(primary_color),
            fontName='Helvetica-Bold'
        )
        story.append(Paragraph("References", section_style))

        for ref in references:
            story.extend(self._format_modern_reference_entry(ref, primary_color))

        return story

    def _format_modern_reference_entry(self, ref: Dict[str, Any], primary_color: str) -> List:
        """Format modern reference entry with card styling."""
        from reportlab.lib.colors import HexColor

        story = []

        # Name with modern styling
        name = ref.get('name', '')
        if name:
            name_style = ParagraphStyle(
                name='ModernRefName',
                parent=self.styles['Normal'],
                fontSize=12,
                spaceAfter=3,
                textColor=HexColor(primary_color),
                fontName='Helvetica-Bold'
            )
            story.append(Paragraph(name, name_style))

        # Position and company
        position = ref.get('position', '')
        company = ref.get('company', '')

        if position and company:
            title_text = f"{position} at {company}"
        elif position:
            title_text = position
        elif company:
            title_text = company
        else:
            title_text = ""

        if title_text:
            title_style = ParagraphStyle(
                name='ModernRefTitle',
                parent=self.styles['Normal'],
                fontSize=10,
                spaceAfter=4,
                textColor=HexColor("#374151")
            )
            story.append(Paragraph(title_text, title_style))

        # Contact information
        contact_parts = []
        if ref.get('email'):
            contact_parts.append(f"✉ {ref['email']}")
        if ref.get('phone'):
            contact_parts.append(f"☎ {ref['phone']}")

        if contact_parts:
            contact_style = ParagraphStyle(
                name='ModernRefContact',
                parent=self.styles['Normal'],
                fontSize=9,
                spaceAfter=15,
                textColor=HexColor("#6B7280")
            )
            story.append(Paragraph(" | ".join(contact_parts), contact_style))

        return story

    def _format_date_range(self, start_date: str, end_date: str, is_current: bool) -> str:
        """Format date range for modern template."""
        if not start_date:
            return ""

        try:
            from datetime import datetime
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            start_formatted = start_dt.strftime("%b %Y")
        except:
            start_formatted = start_date

        if is_current:
            end_formatted = "Present"
        elif end_date:
            try:
                end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                end_formatted = end_dt.strftime("%b %Y")
            except:
                end_formatted = end_date
        else:
            end_formatted = "Present"

        return f"{start_formatted} - {end_formatted}"
