"""
Template management service.

This module provides template management functionality including
template registry, configuration management, and preview generation.
"""

import io
import base64
from datetime import datetime
from typing import Dict, List, Optional, Any
from PIL import Image, ImageDraw, ImageFont
from loguru import logger

from app.schemas.template import TemplateConfiguration, TemplateResponse, TemplateListResponse


class TemplateRegistry:
    """
    Registry for managing CV templates and their configurations.
    
    Provides a centralized way to manage template metadata, configurations,
    and rendering logic without requiring database storage.
    """
    
    def __init__(self):
        """Initialize template registry with predefined templates."""
        self._templates = self._initialize_templates()
    
    def _initialize_templates(self) -> Dict[str, Dict[str, Any]]:
        """Initialize predefined template configurations."""
        return {
            "german": {
                "id": "german",
                "name": "German Template",
                "description": "Professional German CV template following German standards",
                "language": "de",
                "features": [
                    "Multi-page layout",
                    "Cover page with photo",
                    "Table of contents",
                    "Certificate integration",
                    "Cover letter support",
                    "German language translations"
                ],
                "supported_sections": [
                    "personal_info",
                    "education",
                    "work_experience",
                    "skills",
                    "references",
                    "cover_letter"
                ],
                "configuration": TemplateConfiguration(
                    page_size="A4",
                    margins="40pt",
                    font_family="Helvetica",
                    color_scheme={
                        "primary": "#005A9C",
                        "text": "#333333",
                        "secondary": "#444444",
                        "border": "#CCCCCC"
                    },
                    supports_photo=True,
                    supports_certificates=True,
                    supports_cover_letter=True,
                    max_pages=None,
                    sections={
                        "personal_info": {"required": True, "layout": "two_column"},
                        "education": {"required": False, "layout": "chronological", "sort_order": "desc"},
                        "work_experience": {"required": False, "layout": "chronological", "sort_order": "desc"},
                        "skills": {"required": False, "layout": "categorized"},
                        "references": {"required": False, "layout": "list"},
                        "cover_letter": {"required": False, "layout": "letter"}
                    }
                ),
                "created_at": datetime(2024, 1, 1),
                "updated_at": datetime(2024, 1, 1)
            },
            "standard": {
                "id": "standard",
                "name": "Standard Template",
                "description": "Clean, professional template suitable for international use",
                "language": "en",
                "features": [
                    "Single-page layout",
                    "Photo support",
                    "Skills visualization",
                    "Clean typography"
                ],
                "supported_sections": [
                    "personal_info",
                    "education",
                    "work_experience",
                    "skills",
                    "references"
                ],
                "configuration": TemplateConfiguration(
                    page_size="A4",
                    margins="72pt",
                    font_family="Helvetica",
                    color_scheme={
                        "primary": "#2563EB",
                        "text": "#1F2937",
                        "secondary": "#6B7280",
                        "border": "#E5E7EB"
                    },
                    supports_photo=True,
                    supports_certificates=False,
                    supports_cover_letter=False,
                    max_pages=2,
                    sections={
                        "personal_info": {"required": True, "layout": "header"},
                        "education": {"required": False, "layout": "chronological", "sort_order": "desc"},
                        "work_experience": {"required": False, "layout": "chronological", "sort_order": "desc"},
                        "skills": {"required": False, "layout": "grid"},
                        "references": {"required": False, "layout": "compact"}
                    }
                ),
                "created_at": datetime(2024, 1, 1),
                "updated_at": datetime(2024, 1, 1)
            },
            "modern": {
                "id": "modern",
                "name": "Modern Template",
                "description": "Contemporary design with accent colors and modern typography",
                "language": "en",
                "features": [
                    "Modern design",
                    "Accent colors",
                    "Skills progress bars",
                    "Two-column layout"
                ],
                "supported_sections": [
                    "personal_info",
                    "education",
                    "work_experience",
                    "skills",
                    "references"
                ],
                "configuration": TemplateConfiguration(
                    page_size="A4",
                    margins="60pt",
                    font_family="Helvetica",
                    color_scheme={
                        "primary": "#10B981",
                        "text": "#111827",
                        "secondary": "#4B5563",
                        "border": "#D1D5DB"
                    },
                    supports_photo=True,
                    supports_certificates=False,
                    supports_cover_letter=True,
                    max_pages=3,
                    sections={
                        "personal_info": {"required": True, "layout": "sidebar"},
                        "education": {"required": False, "layout": "timeline", "sort_order": "desc"},
                        "work_experience": {"required": False, "layout": "timeline", "sort_order": "desc"},
                        "skills": {"required": False, "layout": "progress_bars"},
                        "references": {"required": False, "layout": "cards"}
                    }
                ),
                "created_at": datetime(2024, 1, 1),
                "updated_at": datetime(2024, 1, 1)
            }
        }
    
    def get_all_templates(self) -> TemplateListResponse:
        """
        Get all available templates.
        
        Returns:
            TemplateListResponse: List of all templates with metadata
        """
        templates = []
        
        for template_data in self._templates.values():
            template_response = TemplateResponse(
                id=template_data["id"],
                name=template_data["name"],
                description=template_data["description"],
                language=template_data["language"],
                features=template_data["features"],
                supported_sections=template_data["supported_sections"],
                preview_image=f"/api/v1/templates/{template_data['id']}/preview",
                configuration=template_data["configuration"],
                created_at=template_data["created_at"],
                updated_at=template_data["updated_at"]
            )
            templates.append(template_response)
        
        return TemplateListResponse(
            templates=templates,
            total=len(templates)
        )
    
    def get_template(self, template_id: str) -> Optional[TemplateResponse]:
        """
        Get a specific template by ID.
        
        Args:
            template_id: Template identifier
            
        Returns:
            TemplateResponse: Template data or None if not found
        """
        template_data = self._templates.get(template_id)
        if not template_data:
            return None
        
        return TemplateResponse(
            id=template_data["id"],
            name=template_data["name"],
            description=template_data["description"],
            language=template_data["language"],
            features=template_data["features"],
            supported_sections=template_data["supported_sections"],
            preview_image=f"/api/v1/templates/{template_data['id']}/preview",
            configuration=template_data["configuration"],
            created_at=template_data["created_at"],
            updated_at=template_data["updated_at"]
        )
    
    def template_exists(self, template_id: str) -> bool:
        """
        Check if a template exists.
        
        Args:
            template_id: Template identifier
            
        Returns:
            bool: True if template exists
        """
        return template_id in self._templates
    
    def get_template_configuration(self, template_id: str) -> Optional[TemplateConfiguration]:
        """
        Get template configuration.
        
        Args:
            template_id: Template identifier
            
        Returns:
            TemplateConfiguration: Template configuration or None if not found
        """
        template_data = self._templates.get(template_id)
        if not template_data:
            return None
        
        return template_data["configuration"]


class TemplatePreviewService:
    """
    Service for generating template preview images.
    
    Creates placeholder preview images for templates using PIL.
    """
    
    def __init__(self):
        """Initialize preview service."""
        self.template_registry = TemplateRegistry()
    
    def generate_preview(
        self,
        template_id: str,
        format: str = "png",
        width: int = 400,
        height: int = 600,
        primary_color: Optional[str] = None
    ) -> Optional[bytes]:
        """
        Generate a preview image for a template.
        
        Args:
            template_id: Template identifier
            format: Image format (png, jpg)
            width: Image width in pixels
            height: Image height in pixels
            primary_color: Primary color override
            
        Returns:
            bytes: Image data or None if template not found
        """
        template = self.template_registry.get_template(template_id)
        if not template:
            return None
        
        try:
            # Create image
            image = Image.new('RGB', (width, height), 'white')
            draw = ImageDraw.Draw(image)
            
            # Use primary color from template or override
            color = primary_color or template.configuration.color_scheme["primary"]
            
            # Draw template preview placeholder
            self._draw_template_preview(draw, template, width, height, color)
            
            # Save to bytes
            buffer = io.BytesIO()
            image.save(buffer, format=format.upper())
            
            logger.info(f"Preview generated for template {template_id}")
            return buffer.getvalue()
            
        except Exception as e:
            logger.error(f"Preview generation failed for template {template_id}: {e}")
            return None
    
    def _draw_template_preview(
        self,
        draw: ImageDraw.Draw,
        template: TemplateResponse,
        width: int,
        height: int,
        primary_color: str
    ):
        """
        Draw template preview elements.
        
        Args:
            draw: PIL ImageDraw instance
            template: Template data
            width: Image width
            height: Image height
            primary_color: Primary color for elements
        """
        # Draw header
        draw.rectangle([20, 20, width-20, 80], fill=primary_color)
        
        # Draw template name (placeholder - would need font file for actual text)
        draw.rectangle([30, 30, width-30, 50], fill='white')
        
        # Draw content sections
        y_pos = 100
        section_height = 40
        
        for i, section in enumerate(template.supported_sections[:4]):
            # Section header
            draw.rectangle([20, y_pos, width-20, y_pos + 20], fill=primary_color, outline='black')
            
            # Section content
            draw.rectangle([30, y_pos + 25, width-30, y_pos + section_height], fill='#f8f9fa', outline='#e9ecef')
            
            y_pos += section_height + 30
        
        # Draw footer
        draw.rectangle([20, height-40, width-20, height-20], fill='#6c757d')


# Global template registry instance
template_registry = TemplateRegistry()
template_preview_service = TemplatePreviewService()
