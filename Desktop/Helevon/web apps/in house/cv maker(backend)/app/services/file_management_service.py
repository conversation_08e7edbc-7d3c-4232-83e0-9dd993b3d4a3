"""
Comprehensive file management service.

This service handles all file operations including photos, certificates,
and their integration with CV data.
"""

import base64
import json
import logging
from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import attributes

from app.models.file import File
from app.models.cv import CV
from app.services.file_validation_service import file_validation_service

logger = logging.getLogger(__name__)


class FileManagementService:
    """Comprehensive file management service."""
    
    async def upload_profile_photo(self, cv_id: str, user_id: str, file_content: bytes, 
                                 filename: str, mime_type: str, db: AsyncSession) -> Tuple[File, bool]:
        """
        Upload and link a profile photo to a CV.
        
        Returns:
            Tuple[File, bool]: The file object and whether an existing photo was replaced
        """
        logger.info(f"Uploading profile photo for CV {cv_id}")
        
        # Validate file
        is_valid, error, info = file_validation_service.validate_file(
            file_content=file_content,
            filename=filename,
            declared_mime_type=mime_type,
            category="photo"
        )
        
        if not is_valid:
            raise ValueError(f"Photo validation failed: {error}")
        
        # Check for existing photo and remove it
        existing_photo = await self._get_cv_photo(cv_id, db)
        replaced = existing_photo is not None
        
        if existing_photo:
            logger.info(f"Replacing existing photo {existing_photo.id}")
            await db.delete(existing_photo)
        
        # Create new photo file
        photo_file = File.create_photo(
            user_id=user_id,
            cv_id=cv_id,
            name=info['sanitized_filename'],
            file_data=file_content,
            mime_type=info['actual_mime_type']
        )
        
        db.add(photo_file)
        await db.flush()  # Get the ID
        
        # Update CV personal_info with photo ID
        await self._link_photo_to_cv(cv_id, photo_file.id, db)
        
        await db.commit()
        logger.info(f"Profile photo uploaded successfully: {photo_file.id}")
        
        return photo_file, replaced
    
    async def upload_education_certificate(self, cv_id: str, education_entry_id: str, 
                                         user_id: str, file_content: bytes, filename: str, 
                                         mime_type: str, db: AsyncSession) -> File:
        """Upload and link a certificate to an education entry."""
        logger.info(f"Uploading certificate for education {education_entry_id} in CV {cv_id}")
        
        # Validate file
        is_valid, error, info = file_validation_service.validate_file(
            file_content=file_content,
            filename=filename,
            declared_mime_type=mime_type,
            category="certificate"
        )
        
        if not is_valid:
            raise ValueError(f"Certificate validation failed: {error}")
        
        # Verify education entry exists
        cv = await self._get_cv_with_verification(cv_id, user_id, db)
        education_entry = self._find_education_entry(cv, education_entry_id)
        
        if not education_entry:
            raise ValueError(f"Education entry {education_entry_id} not found")
        
        # Create certificate file
        cert_file = File.create_education_certificate(
            user_id=user_id,
            cv_id=cv_id,
            education_entry_id=education_entry_id,
            name=info['sanitized_filename'],
            file_data=file_content,
            mime_type=info['actual_mime_type']
        )
        
        db.add(cert_file)
        await db.flush()  # Get the ID
        
        # Link certificate to education entry
        await self._link_certificate_to_education(cv_id, education_entry_id, cert_file.id, db)
        
        await db.commit()
        logger.info(f"Education certificate uploaded successfully: {cert_file.id}")
        
        return cert_file
    
    async def upload_standalone_certificate(self, cv_id: str, user_id: str, file_content: bytes,
                                          filename: str, mime_type: str, description: Optional[str] = None,
                                          issue_date: Optional[str] = None,
                                          issuing_organization: Optional[str] = None,
                                          db: AsyncSession = None) -> File:
        """Upload a standalone certificate."""
        logger.info(f"Uploading standalone certificate for CV {cv_id}")
        
        # Validate file
        is_valid, error, info = file_validation_service.validate_file(
            file_content=file_content,
            filename=filename,
            declared_mime_type=mime_type,
            category="certificate"
        )
        
        if not is_valid:
            raise ValueError(f"Certificate validation failed: {error}")
        
        # Parse issue date if provided
        parsed_date = None
        if issue_date:
            from datetime import datetime
            try:
                parsed_date = datetime.fromisoformat(issue_date.replace('Z', '+00:00'))
            except ValueError:
                logger.warning(f"Invalid issue date format: {issue_date}")
        
        # Create standalone certificate
        cert_file = File.create_standalone_certificate(
            user_id=user_id,
            cv_id=cv_id,
            name=info['sanitized_filename'],
            file_data=file_content,
            mime_type=info['actual_mime_type'],
            description=description,
            issue_date=parsed_date,
            issuing_organization=issuing_organization
        )
        
        db.add(cert_file)
        await db.commit()
        
        logger.info(f"Standalone certificate uploaded successfully: {cert_file.id}")
        return cert_file
    
    async def get_cv_files(self, cv_id: str, user_id: str, db: AsyncSession) -> Dict[str, List[File]]:
        """Get all files associated with a CV, organized by type."""
        result = await db.execute(
            select(File).where(
                and_(File.cv_id == cv_id, File.user_id == user_id)
            ).order_by(File.created_at.desc())
        )
        files = result.scalars().all()
        
        organized = {
            'photos': [],
            'education_certificates': [],
            'standalone_certificates': []
        }
        
        for file in files:
            if file.is_photo:
                organized['photos'].append(file)
            elif file.is_education_linked:
                organized['education_certificates'].append(file)
            elif file.is_standalone_certificate:
                organized['standalone_certificates'].append(file)
        
        return organized
    
    async def delete_file(self, file_id: str, user_id: str, db: AsyncSession) -> bool:
        """Delete a file and clean up CV references."""
        result = await db.execute(
            select(File).where(and_(File.id == file_id, File.user_id == user_id))
        )
        file = result.scalar_one_or_none()
        
        if not file:
            return False
        
        # Clean up CV references
        if file.is_photo:
            await self._unlink_photo_from_cv(file.cv_id, file_id, db)
        elif file.is_education_linked:
            await self._unlink_certificate_from_education(file.cv_id, file.education_entry_id, file_id, db)
        
        await db.delete(file)
        await db.commit()
        
        logger.info(f"File deleted successfully: {file_id}")
        return True
    
    # Private helper methods
    
    async def _get_cv_photo(self, cv_id: str, db: AsyncSession) -> Optional[File]:
        """Get the current profile photo for a CV."""
        result = await db.execute(
            select(File).where(
                and_(File.cv_id == cv_id, File.category == 'photo')
            )
        )
        return result.scalar_one_or_none()
    
    async def _get_cv_with_verification(self, cv_id: str, user_id: str, db: AsyncSession) -> CV:
        """Get CV with user verification."""
        result = await db.execute(
            select(CV).where(and_(CV.id == cv_id, CV.user_id == user_id))
        )
        cv = result.scalar_one_or_none()
        
        if not cv:
            raise ValueError(f"CV {cv_id} not found or access denied")
        
        return cv
    
    def _find_education_entry(self, cv: CV, education_entry_id: str) -> Optional[Dict[str, Any]]:
        """Find an education entry by ID."""
        for entry in cv.education or []:
            if entry.get('id') == education_entry_id:
                return entry
        return None
    
    async def _link_photo_to_cv(self, cv_id: str, photo_id: str, db: AsyncSession):
        """Link a photo ID to CV personal_info."""
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one()
        
        personal_info = cv.personal_info or {}
        personal_info['photoUrl'] = photo_id
        cv.personal_info = dict(personal_info)  # Force SQLAlchemy to detect change
        
        attributes.flag_modified(cv, 'personal_info')
        logger.debug(f"Linked photo {photo_id} to CV {cv_id}")
    
    async def _link_certificate_to_education(self, cv_id: str, education_entry_id: str, 
                                           cert_id: str, db: AsyncSession):
        """Link a certificate ID to an education entry."""
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one()
        
        education = cv.education or []
        for entry in education:
            if entry.get('id') == education_entry_id:
                certificates = entry.get('certificates', [])
                if cert_id not in certificates:
                    certificates.append(cert_id)
                    entry['certificates'] = certificates
                break
        
        cv.education = list(education)  # Force SQLAlchemy to detect change
        attributes.flag_modified(cv, 'education')
        logger.debug(f"Linked certificate {cert_id} to education {education_entry_id}")
    
    async def _unlink_photo_from_cv(self, cv_id: str, photo_id: str, db: AsyncSession):
        """Remove photo ID from CV personal_info."""
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()
        
        if cv and cv.personal_info:
            personal_info = cv.personal_info
            if personal_info.get('photoUrl') == photo_id:
                personal_info['photoUrl'] = None
                cv.personal_info = dict(personal_info)
                attributes.flag_modified(cv, 'personal_info')
    
    async def _unlink_certificate_from_education(self, cv_id: str, education_entry_id: str, 
                                               cert_id: str, db: AsyncSession):
        """Remove certificate ID from education entry."""
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()
        
        if cv and cv.education:
            education = cv.education
            for entry in education:
                if entry.get('id') == education_entry_id:
                    certificates = entry.get('certificates', [])
                    if cert_id in certificates:
                        certificates.remove(cert_id)
                        entry['certificates'] = certificates
                    break
            
            cv.education = list(education)
            attributes.flag_modified(cv, 'education')


# Global service instance
file_management_service = FileManagementService()
