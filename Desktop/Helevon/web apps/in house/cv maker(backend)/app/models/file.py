"""
File model for file upload and management.

This module contains the File model for handling file uploads,
storage, and retrieval with base64 encoding.
"""

import uuid
from datetime import datetime
from typing import Optional
from sqlalchemy import String, DateTime, Integer, Text, ForeignKey, LargeBinary, UniqueConstraint, Index
from sqlalchemy.orm import Mapped, mapped_column, relationship

from sqlalchemy.sql import func

from app.core.database import Base


class File(Base):
    """
    File model for managing uploaded files.

    Handles file uploads with binary blob storage for database compatibility,
    file metadata, and associations with users and CVs.
    """

    __tablename__ = "files"

    # Primary key - String UUID for compatibility
    id: Mapped[str] = mapped_column(
        String(36),
        primary_key=True,
        default=lambda: str(uuid.uuid4()),
        index=True
    )

    # Foreign keys
    user_id: Mapped[str] = mapped_column(
        String(36),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )

    cv_id: Mapped[Optional[str]] = mapped_column(
        String(36),
        ForeignKey("cvs.id", ondelete="CASCADE"),
        nullable=True,
        index=True
    )

    # Education entry ID for certificate linking (stored as string for consistency)
    education_entry_id: Mapped[Optional[str]] = mapped_column(
        String(36),
        nullable=True,
        index=True
    )

    # File metadata
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    type: Mapped[str] = mapped_column(String(100), nullable=False)  # MIME type
    size: Mapped[int] = mapped_column(Integer, nullable=False)  # Size in bytes

    # File data stored as binary blob (compatible with SQLite and PostgreSQL)
    file_data: Mapped[bytes] = mapped_column(LargeBinary, nullable=False)

    # File category for organization
    category: Mapped[str] = mapped_column(String(50), nullable=False)  # photo, certificate, signature, other

    # Certificate-specific metadata (for standalone certificates)
    file_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    issue_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    issuing_organization: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False
    )

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="files")
    cv: Mapped[Optional["CV"]] = relationship("CV", back_populates="files")

    # Table constraints
    __table_args__ = (
        # Index for efficient certificate queries
        Index('ix_files_cv_education', 'cv_id', 'education_entry_id'),
        Index('ix_files_category_cv', 'category', 'cv_id'),
    )

    @property
    def is_photo(self) -> bool:
        """Check if this file is a profile photo."""
        return self.category == 'photo'

    @property
    def is_certificate(self) -> bool:
        """Check if this file is a certificate."""
        return self.category == 'certificate'

    @property
    def is_education_linked(self) -> bool:
        """Check if this certificate is linked to an education entry."""
        return self.is_certificate and self.education_entry_id is not None

    @property
    def is_standalone_certificate(self) -> bool:
        """Check if this is a standalone certificate."""
        return self.is_certificate and self.education_entry_id is None

    def get_file_url(self) -> str:
        """Generate the file access URL."""
        return f"/api/v1/files/{self.id}"

    @classmethod
    def create_photo(cls, user_id: str, cv_id: str, name: str, file_data: bytes, mime_type: str) -> "File":
        """Create a new profile photo file."""
        return cls(
            user_id=user_id,
            cv_id=cv_id,
            name=name,
            type=mime_type,
            size=len(file_data),
            file_data=file_data,
            category='photo'
        )

    @classmethod
    def create_education_certificate(cls, user_id: str, cv_id: str, education_entry_id: str,
                                   name: str, file_data: bytes, mime_type: str) -> "File":
        """Create a new education-linked certificate."""
        return cls(
            user_id=user_id,
            cv_id=cv_id,
            education_entry_id=education_entry_id,
            name=name,
            type=mime_type,
            size=len(file_data),
            file_data=file_data,
            category='certificate'
        )

    @classmethod
    def create_standalone_certificate(cls, user_id: str, cv_id: str, name: str, file_data: bytes,
                                    mime_type: str, description: Optional[str] = None,
                                    issue_date: Optional[datetime] = None,
                                    issuing_organization: Optional[str] = None) -> "File":
        """Create a new standalone certificate."""
        return cls(
            user_id=user_id,
            cv_id=cv_id,
            name=name,
            type=mime_type,
            size=len(file_data),
            file_data=file_data,
            category='certificate',
            file_description=description,
            issue_date=issue_date,
            issuing_organization=issuing_organization
        )
    
    def __repr__(self) -> str:
        return f"<File(id={self.id}, name={self.name}, type={self.type}, category={self.category})>"
    
    def get_category_options(self) -> list[str]:
        """Get available file category options."""
        return ["photo", "certificate", "cover_letter", "other"]
    
    def is_owned_by(self, user_id: str) -> bool:
        """Check if file is owned by the specified user."""
        return self.user_id == user_id
    
    def is_image(self) -> bool:
        """Check if file is an image."""
        return self.type.startswith("image/")
    
    def is_pdf(self) -> bool:
        """Check if file is a PDF."""
        return self.type == "application/pdf"
    
    def get_size_mb(self) -> float:
        """Get file size in megabytes."""
        return self.size / (1024 * 1024)
    
    def generate_url(self, base_url: str = "") -> str:
        """
        Generate access URL for the file.
        
        Args:
            base_url: Base URL for the API
            
        Returns:
            str: Generated file access URL
        """
        if self.cv_id:
            return f"{base_url}/api/v1/cv/{self.cv_id}/file/{self.id}"
        else:
            return f"{base_url}/api/v1/file/{self.id}"
    
    def validate_file_type(self, allowed_types: list[str]) -> bool:
        """
        Validate if file type is allowed.
        
        Args:
            allowed_types: List of allowed MIME types
            
        Returns:
            bool: True if file type is allowed
        """
        return self.type in allowed_types
    
    def validate_file_size(self, max_size_mb: int) -> bool:
        """
        Validate if file size is within limits.
        
        Args:
            max_size_mb: Maximum allowed size in MB
            
        Returns:
            bool: True if file size is within limits
        """
        return self.get_size_mb() <= max_size_mb
