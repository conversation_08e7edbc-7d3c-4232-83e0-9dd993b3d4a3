"""
CV model for curriculum vitae management.

This module contains the CV model with all sections and data structures
for managing curriculum vitae documents.
"""

import uuid
from datetime import datetime
from typing import Optional
from sqlalchemy import String, DateTime, Text, ForeignKey, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship

from sqlalchemy.sql import func

from app.core.database import Base


class CV(Base):
    """
    CV model for managing curriculum vitae documents.
    
    Stores all CV data including personal information, education, work experience,
    skills, references, and cover letter in JSON format for flexibility.
    """
    
    __tablename__ = "cvs"
    
    # Primary key - String UUID for compatibility
    id: Mapped[str] = mapped_column(
        String(36),
        primary_key=True,
        default=lambda: str(uuid.uuid4()),
        index=True
    )

    # Foreign key to user
    user_id: Mapped[str] = mapped_column(
        String(36),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Basic CV information
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    template: Mapped[str] = mapped_column(String(50), default="standard", nullable=False)
    language: Mapped[str] = mapped_column(String(5), default="de", nullable=False)
    
    # CV sections stored as JSON for flexibility
    personal_info: Mapped[dict] = mapped_column(JSON, default=dict, nullable=False)
    education: Mapped[list] = mapped_column(JSON, default=list, nullable=False)
    work_experience: Mapped[list] = mapped_column(JSON, default=list, nullable=False)
    skills: Mapped[list] = mapped_column(JSON, default=list, nullable=False)
    references: Mapped[list] = mapped_column(JSON, default=list, nullable=False)
    
    # Cover letter can be string or structured object
    cover_letter: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False
    )
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="cvs")
    files: Mapped[list["File"]] = relationship("File", back_populates="cv", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<CV(id={self.id}, title={self.title}, user_id={self.user_id})>"
    
    def initialize_empty_sections(self):
        """Initialize CV with empty sections."""
        if not self.personal_info:
            self.personal_info = {
                "firstName": "",
                "lastName": "",
                "email": "",
                "phone": "",
                "address": "",
                "city": "",
                "postalCode": "",
                "country": "",
                "dateOfBirth": None,
                "placeOfBirth": "",
                "nationality": "",
                "maritalStatus": "",
                "photoUrl": None
            }
        
        if not self.education:
            self.education = []
        
        if not self.work_experience:
            self.work_experience = []
        
        if not self.skills:
            self.skills = []
        
        if not self.references:
            self.references = []
        
        if not self.cover_letter:
            self.cover_letter = ""
    
    def get_template_options(self) -> list[str]:
        """Get available template options."""
        return ["standard", "modern", "creative", "german"]
    
    def get_language_options(self) -> list[str]:
        """Get available language options."""
        return ["en", "de", "ar"]
    
    def is_owned_by(self, user_id: str) -> bool:
        """Check if CV is owned by the specified user."""
        return self.user_id == user_id
