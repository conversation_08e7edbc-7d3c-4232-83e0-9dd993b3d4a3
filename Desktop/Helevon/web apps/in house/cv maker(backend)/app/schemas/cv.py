"""
CV-related Pydantic schemas.

This module contains all schemas related to CV management,
including CV sections and structured data.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from pydantic import Field, EmailStr, validator

from app.schemas.base import BaseSchema, TimestampMixin, LanguageType, TemplateType, SkillCategoryType, SkillLevelType


class PersonalInfo(BaseSchema):
    """Schema for personal information section."""
    
    firstName: str = Field(..., min_length=1, max_length=100, description="First name")
    lastName: str = Field(..., min_length=1, max_length=100, description="Last name")
    email: EmailStr = Field(..., description="Email address")
    phone: str = Field(..., min_length=1, max_length=50, description="Phone number")
    address: str = Field(..., min_length=1, max_length=255, description="Street address")
    city: str = Field(..., min_length=1, max_length=100, description="City")
    postalCode: str = Field(..., min_length=1, max_length=20, description="Postal code")
    country: str = Field(..., min_length=1, max_length=100, description="Country")
    dateOfBirth: Optional[str] = Field(None, description="Date of birth (YYYY-MM-DD)")
    placeOfBirth: Optional[str] = Field(None, max_length=100, description="Place of birth")
    nationality: Optional[str] = Field(None, max_length=100, description="Nationality")
    maritalStatus: Optional[str] = Field(None, max_length=50, description="Marital status")
    photoUrl: Optional[str] = Field(None, description="Photo URL or file ID")


class EducationEntry(BaseSchema):
    """Schema for education entry."""
    
    id: str = Field(..., description="Education entry ID")
    institution: str = Field(..., min_length=1, max_length=255, description="Educational institution")
    degree: str = Field(..., min_length=1, max_length=255, description="Degree or qualification")
    fieldOfStudy: str = Field(..., min_length=1, max_length=255, description="Field of study")
    startDate: str = Field(..., description="Start date (YYYY-MM-DD)")
    endDate: Optional[str] = Field(None, description="End date (YYYY-MM-DD)")
    isCurrentlyStudying: bool = Field(default=False, description="Currently studying flag")
    grade: Optional[str] = Field(None, max_length=50, description="Grade or GPA")
    description: Optional[str] = Field(None, max_length=1000, description="Additional description")
    certificates: List[str] = Field(default=[], description="List of certificate IDs")


class WorkExperienceEntry(BaseSchema):
    """Schema for work experience entry."""
    
    id: str = Field(..., description="Work experience entry ID")
    company: str = Field(..., min_length=1, max_length=255, description="Company name")
    position: str = Field(..., min_length=1, max_length=255, description="Job position")
    startDate: str = Field(..., description="Start date (YYYY-MM-DD)")
    endDate: Optional[str] = Field(None, description="End date (YYYY-MM-DD)")
    isCurrentlyWorking: bool = Field(default=False, description="Currently working flag")
    description: Optional[str] = Field(None, max_length=2000, description="Job description")
    location: Optional[str] = Field(None, max_length=255, description="Work location")


class SkillEntry(BaseSchema):
    """Schema for skill entry."""

    id: Optional[str] = Field(None, description="Skill entry ID (auto-generated)")
    name: str = Field(..., min_length=1, max_length=100, description="Skill name")
    category: str = SkillCategoryType
    level: str = SkillLevelType


class SkillCreate(BaseSchema):
    """Schema for creating a new skill entry."""

    name: str = Field(..., min_length=1, max_length=100, description="Skill name")
    category: str = SkillCategoryType
    level: str = SkillLevelType


class SkillUpdate(BaseSchema):
    """Schema for updating a skill entry."""

    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Skill name")
    category: Optional[str] = SkillCategoryType
    level: Optional[str] = SkillLevelType


class CoverLetterData(BaseSchema):
    """Schema for cover letter data."""

    recipientName: Optional[str] = Field(None, max_length=255, description="Recipient name")
    company: Optional[str] = Field(None, max_length=255, description="Company name")
    address: Optional[str] = Field(None, max_length=500, description="Company address")
    postalCode: Optional[str] = Field(None, max_length=20, description="Postal code")
    city: Optional[str] = Field(None, max_length=100, description="City")
    country: Optional[str] = Field(None, max_length=100, description="Country")
    email: Optional[str] = Field(None, description="Company email")
    phone: Optional[str] = Field(None, max_length=50, description="Company phone")
    otherInformation: Optional[str] = Field(None, max_length=500, description="Other information")
    subject: Optional[str] = Field(None, max_length=255, description="Cover letter subject (defaults to CV title)")
    date: Optional[str] = Field(None, description="Date (YYYY-MM-DD, defaults to current date)")
    content: str = Field(..., min_length=1, max_length=5000, description="Cover letter content (supports rich text)")
    signatureFileId: Optional[str] = Field(None, description="Signature file ID")


class CoverLetterUpdate(BaseSchema):
    """Schema for cover letter update."""

    recipientName: Optional[str] = Field(None, max_length=255, description="Recipient name")
    company: Optional[str] = Field(None, max_length=255, description="Company name")
    address: Optional[str] = Field(None, max_length=500, description="Company address")
    postalCode: Optional[str] = Field(None, max_length=20, description="Postal code")
    city: Optional[str] = Field(None, max_length=100, description="City")
    country: Optional[str] = Field(None, max_length=100, description="Country")
    email: Optional[str] = Field(None, description="Company email")
    phone: Optional[str] = Field(None, max_length=50, description="Company phone")
    otherInformation: Optional[str] = Field(None, max_length=500, description="Other information")
    subject: Optional[str] = Field(None, max_length=255, description="Cover letter subject")
    date: Optional[str] = Field(None, description="Date (YYYY-MM-DD)")
    content: Optional[str] = Field(None, min_length=1, max_length=5000, description="Cover letter content")
    signatureFileId: Optional[str] = Field(None, description="Signature file ID")


class ReferenceEntry(BaseSchema):
    """Schema for reference entry."""

    id: str = Field(..., description="Reference entry ID")
    name: str = Field(..., min_length=1, max_length=255, description="Reference name")
    position: str = Field(..., min_length=1, max_length=255, description="Reference position")
    company: str = Field(..., min_length=1, max_length=255, description="Reference company")
    email: EmailStr = Field(..., description="Reference email")
    phone: str = Field(..., min_length=1, max_length=50, description="Reference phone")


class CVBase(BaseSchema):
    """Base CV schema with common fields."""
    
    title: str = Field(..., min_length=1, max_length=255, description="CV title")
    template: str = TemplateType
    language: str = LanguageType


class CVCreate(CVBase):
    """Schema for CV creation."""
    
    user_id: str = Field(..., description="User ID (UUID)")


class CVUpdate(BaseSchema):
    """Schema for CV updates."""
    
    title: Optional[str] = Field(None, min_length=1, max_length=255, description="CV title")
    template: Optional[str] = Field(None, pattern="^(standard|modern|creative|german-ausbildung|german)$", description="CV template")
    language: Optional[str] = Field(None, pattern="^(en|de)$", description="CV language")


class PersonalInfoUpdate(BaseSchema):
    """Schema for personal info section update."""

    firstName: Optional[str] = Field(None, min_length=1, max_length=100, description="First name")
    lastName: Optional[str] = Field(None, min_length=1, max_length=100, description="Last name")
    email: Optional[EmailStr] = Field(None, description="Email address")
    phone: Optional[str] = Field(None, min_length=1, max_length=50, description="Phone number")
    address: Optional[str] = Field(None, min_length=1, max_length=255, description="Street address")
    city: Optional[str] = Field(None, min_length=1, max_length=100, description="City")
    postalCode: Optional[str] = Field(None, min_length=1, max_length=20, description="Postal code")
    country: Optional[str] = Field(None, min_length=1, max_length=100, description="Country")
    dateOfBirth: Optional[str] = Field(None, description="Date of birth (YYYY-MM-DD)")
    placeOfBirth: Optional[str] = Field(None, max_length=100, description="Place of birth")
    nationality: Optional[str] = Field(None, max_length=100, description="Nationality")
    maritalStatus: Optional[str] = Field(None, max_length=50, description="Marital status")
    summary: Optional[str] = Field(None, max_length=1000, description="Professional summary")
    # photoUrl removed - managed separately via file upload endpoints


class EducationUpdate(BaseSchema):
    """Schema for education section update."""
    
    education: List[EducationEntry] = Field(..., description="List of education entries")


class WorkExperienceUpdate(BaseSchema):
    """Schema for work experience section update."""
    
    workExperience: List[WorkExperienceEntry] = Field(..., description="List of work experience entries")


class SkillsUpdate(BaseSchema):
    """Schema for skills section update."""
    
    skills: List[SkillEntry] = Field(..., description="List of skill entries")


class ReferencesUpdate(BaseSchema):
    """Schema for references section update."""
    
    references: List[ReferenceEntry] = Field(..., description="List of reference entries")


class CoverLetterSectionUpdate(BaseSchema):
    """Schema for cover letter section update."""

    coverLetter: CoverLetterData = Field(..., description="Cover letter data")


class CVResponse(CVBase, TimestampMixin):
    """Schema for CV response data."""
    
    id: str = Field(..., description="CV unique identifier")
    user_id: str = Field(..., description="User ID")
    personal_info: Dict[str, Any] = Field(default={}, description="Personal information")
    education: List[Dict[str, Any]] = Field(default=[], description="Education entries")
    work_experience: List[Dict[str, Any]] = Field(default=[], description="Work experience entries")
    skills: List[Dict[str, Any]] = Field(default=[], description="Skill entries")
    references: List[Dict[str, Any]] = Field(default=[], description="Reference entries")
    cover_letter: Optional[Dict[str, Any]] = Field(None, description="Cover letter data")


class CVWithFiles(CVResponse):
    """Schema for CV response with associated files."""
    
    files: List[Dict[str, Any]] = Field(default=[], description="Associated files")


class CVListResponse(BaseSchema):
    """Schema for CV list response."""
    
    id: str = Field(..., description="CV unique identifier")
    user_id: str = Field(..., description="User ID")
    title: str = Field(..., description="CV title")
    template: str = Field(..., description="CV template")
    language: str = Field(..., description="CV language")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
