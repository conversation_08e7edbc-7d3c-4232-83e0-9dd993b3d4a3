"""
Base Pydantic schemas and common types.

This module contains base schemas and common types used across
the application for consistent validation and serialization.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, ConfigDict


class BaseSchema(BaseModel):
    """Base schema with common configuration."""
    
    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        arbitrary_types_allowed=True,
        str_strip_whitespace=True
    )


class TimestampMixin(BaseSchema):
    """Mixin for models with timestamp fields."""
    
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


class PaginationParams(BaseSchema):
    """Schema for pagination parameters."""
    
    page: int = Field(default=1, ge=1, description="Page number")
    size: int = Field(default=20, ge=1, le=100, description="Page size")


class PaginatedResponse(BaseSchema):
    """Schema for paginated responses."""
    
    items: List[Any] = Field(..., description="List of items")
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")


class ErrorDetail(BaseSchema):
    """Schema for error details."""
    
    field: Optional[str] = Field(None, description="Field that caused the error")
    message: str = Field(..., description="Error message")


class ErrorResponse(BaseSchema):
    """Schema for error responses."""
    
    message: str = Field(..., description="Main error message")
    errors: Optional[List[ErrorDetail]] = Field(None, description="Detailed errors")


class SuccessResponse(BaseSchema):
    """Schema for success responses."""
    
    message: str = Field(..., description="Success message")
    data: Optional[Dict[str, Any]] = Field(None, description="Additional data")


class HealthResponse(BaseSchema):
    """Schema for health check response."""
    
    status: str = Field(..., description="Health status")
    service: str = Field(..., description="Service name")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Check timestamp")


# Common field validators and types
LanguageType = Field(..., pattern="^(en|de|ar)$", description="Language code (en, de, ar)")
TemplateType = Field(..., pattern="^(standard|modern|creative|german|german)$", description="CV template type")
SkillCategoryType = Field(..., pattern="^(technical|language|soft)$", description="Skill category")
SkillLevelType = Field(..., pattern="^(beginner|intermediate|advanced|expert|native)$", description="Skill level")
FileCategoryType = Field(..., pattern="^(photo|certificate|cover_letter|other)$", description="File category")
