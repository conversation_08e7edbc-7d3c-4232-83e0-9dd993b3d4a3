"""
Pydantic schemas package.

This package contains all Pydantic v2 schemas for request/response
validation and serialization.
"""

from app.schemas.base import (
    BaseSchema,
    TimestampMixin,
    PaginationParams,
    PaginatedResponse,
    ErrorDetail,
    ErrorResponse,
    SuccessResponse,
    HealthResponse
)

from app.schemas.user import (
    UserBase,
    UserCreate,
    UserLogin,
    UserUpdate,
    UserResponse,
    UserProfile,
    PasswordVerification,
    PasswordVerificationResponse,
    TokenResponse,
    RefreshTokenRequest,
    TokenData
)

from app.schemas.cv import (
    PersonalInfo,
    EducationEntry,
    WorkExperienceEntry,
    SkillEntry,
    SkillCreate,
    SkillUpdate,
    ReferenceEntry,
    CoverLetterData,
    CoverLetterUpdate,
    CVBase,
    CVCreate,
    CVUpdate,
    PersonalInfoUpdate,
    EducationUpdate,
    WorkExperienceUpdate,
    SkillsUpdate,
    ReferencesUpdate,
    CoverLetterSectionUpdate,
    CVResponse,
    CVWithFiles,
    CVListResponse
)

from app.schemas.file import (
    FileUpload,
    FileBase,
    FileCreate,
    FileUpdate,
    FileResponse,
    FileWithData,
    CertificateBase,
    CertificateCreate,
    CertificateUpdate,
    CertificateResponse,
    CertificateWithData,
    FileListResponse,
    FileDeleteResponse
)

from app.schemas.template import (
    TemplateConfiguration,
    TemplateBase,
    TemplateResponse,
    TemplateListResponse,
    TemplatePreviewRequest,
    CVExportRequest
)

from app.schemas.activity import (
    UserActivityBase,
    UserActivityCreate,
    UserActivityResponse,
    ActivityMetricsParams,
    LoginFrequencyMetric,
    ActionFrequencyMetric,
    PopularActionMetric,
    ActivitySummary,
    UserActivityStats,
    MetricsResponse,
    ActivityExportParams
)

__all__ = [
    # Base schemas
    "BaseSchema", "TimestampMixin", "PaginationParams", "PaginatedResponse",
    "ErrorDetail", "ErrorResponse", "SuccessResponse", "HealthResponse",

    # User schemas
    "UserBase", "UserCreate", "UserLogin", "UserUpdate", "UserResponse",
    "UserProfile", "PasswordVerification", "PasswordVerificationResponse",
    "TokenResponse", "RefreshTokenRequest", "TokenData",

    # CV schemas
    "PersonalInfo", "EducationEntry", "WorkExperienceEntry", "SkillEntry", "SkillCreate", "SkillUpdate",
    "ReferenceEntry", "CoverLetterData", "CoverLetterUpdate", "CVBase", "CVCreate", "CVUpdate",
    "PersonalInfoUpdate", "EducationUpdate", "WorkExperienceUpdate", "SkillsUpdate", "ReferencesUpdate",
    "CoverLetterSectionUpdate", "CVResponse", "CVWithFiles", "CVListResponse",

    # File schemas
    "FileUpload", "FileBase", "FileCreate", "FileUpdate", "FileResponse",
    "FileWithData", "CertificateBase", "CertificateCreate", "CertificateUpdate",
    "CertificateResponse", "CertificateWithData", "FileListResponse", "FileDeleteResponse",

    # Template schemas
    "TemplateConfiguration", "TemplateBase", "TemplateResponse", "TemplateListResponse",
    "TemplatePreviewRequest", "CVExportRequest",

    # Activity schemas
    "UserActivityBase", "UserActivityCreate", "UserActivityResponse",
    "ActivityMetricsParams", "LoginFrequencyMetric", "ActionFrequencyMetric",
    "PopularActionMetric", "ActivitySummary", "UserActivityStats",
    "MetricsResponse", "ActivityExportParams"
]