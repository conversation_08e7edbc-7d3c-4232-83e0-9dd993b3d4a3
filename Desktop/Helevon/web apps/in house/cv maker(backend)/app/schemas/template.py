"""
Template-related Pydantic schemas.

This module contains all schemas related to template management,
template configuration, and template responses.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import Field, validator

from app.schemas.base import BaseSchema, TimestampMixin


class TemplateConfiguration(BaseSchema):
    """Schema for template configuration."""
    
    page_size: str = Field(default="A4", description="Page size (A4, Letter, etc.)")
    margins: str = Field(default="40pt", description="Page margins")
    font_family: str = Field(default="Helvetica", description="Font family")
    color_scheme: Dict[str, str] = Field(
        default={
            "primary": "#005A9C",
            "text": "#333333", 
            "secondary": "#444444",
            "border": "#CCCCCC"
        },
        description="Color scheme configuration"
    )
    supports_photo: bool = Field(default=True, description="Whether template supports photo")
    supports_certificates: bool = Field(default=True, description="Whether template supports certificates")
    supports_cover_letter: bool = Field(default=True, description="Whether template supports cover letter")
    max_pages: Optional[int] = Field(None, description="Maximum pages (null for unlimited)")
    sections: Dict[str, Dict[str, Any]] = Field(
        default={
            "personal_info": {"required": True, "layout": "two_column"},
            "education": {"required": False, "layout": "chronological", "sort_order": "desc"},
            "work_experience": {"required": False, "layout": "chronological", "sort_order": "desc"},
            "skills": {"required": False, "layout": "categorized"},
            "references": {"required": False, "layout": "list"},
            "cover_letter": {"required": False, "layout": "letter"}
        },
        description="Section configurations"
    )


class TemplateBase(BaseSchema):
    """Base template schema with common fields."""
    
    id: str = Field(..., description="Template unique identifier")
    name: str = Field(..., min_length=1, max_length=255, description="Template display name")
    description: str = Field(..., min_length=1, max_length=1000, description="Template description")
    language: str = Field(..., pattern="^(en|de|ar)$", description="Template primary language")
    features: List[str] = Field(default=[], description="Template features list")
    supported_sections: List[str] = Field(
        default=["personal_info", "education", "work_experience", "skills", "references"],
        description="Supported CV sections"
    )


class TemplateResponse(TemplateBase, TimestampMixin):
    """Schema for template response data."""
    
    preview_image: str = Field(..., description="Preview image URL")
    configuration: TemplateConfiguration = Field(..., description="Template configuration")
    
    class Config:
        from_attributes = True


class TemplateListResponse(BaseSchema):
    """Schema for template list response."""
    
    templates: List[TemplateResponse] = Field(..., description="List of available templates")
    total: int = Field(..., description="Total number of templates")


class TemplatePreviewRequest(BaseSchema):
    """Schema for template preview request parameters."""
    
    format: str = Field(default="png", pattern="^(png|jpg|jpeg)$", description="Image format")
    width: int = Field(default=400, ge=100, le=1200, description="Preview width in pixels")
    height: int = Field(default=600, ge=150, le=1800, description="Preview height in pixels")
    primary_color: Optional[str] = Field(None, pattern="^#[0-9A-Fa-f]{6}$", description="Primary color override")


class CVExportRequest(BaseSchema):
    """Schema for CV export request parameters."""
    
    template_id: Optional[str] = Field(None, description="Template ID override")
    include_certificates: bool = Field(default=True, description="Include certificate attachments")
    include_cover_letter: bool = Field(default=True, description="Include cover letter")
    primary_color: Optional[str] = Field(None, pattern="^#[0-9A-Fa-f]{6}$", description="Primary color override")
