"""
Template management endpoints.

This module provides REST API endpoints for template management,
including template listing, details, and preview generation.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import Response
from loguru import logger

from app.core.dependencies import get_current_user
from app.models.user import User
from app.schemas.template import (
    TemplateListResponse,
    TemplateResponse,
    TemplatePreviewRequest
)
from app.services.template_service import template_registry, template_preview_service


router = APIRouter()


@router.get("", response_model=TemplateListResponse)
async def get_templates(
    current_user: User = Depends(get_current_user)
):
    """
    Get list of available CV templates.
    
    Returns all available templates with their metadata, features,
    and configuration details.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        TemplateListResponse: List of available templates
        
    Raises:
        HTTPException: If user not authenticated
    """
    try:
        templates = template_registry.get_all_templates()
        
        logger.debug(f"Retrieved {templates.total} templates for user {current_user.email}")
        
        return templates
        
    except Exception as e:
        logger.error(f"Error retrieving templates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve templates"
        )


@router.get("/{template_id}", response_model=TemplateResponse)
async def get_template_details(
    template_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Get detailed information about a specific template.
    
    Returns comprehensive template information including configuration,
    supported sections, and features.
    
    Args:
        template_id: Template unique identifier
        current_user: Current authenticated user
        
    Returns:
        TemplateResponse: Template details
        
    Raises:
        HTTPException: If template not found or user not authenticated
    """
    try:
        template = template_registry.get_template(template_id)
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Template '{template_id}' not found"
            )
        
        logger.debug(f"Retrieved template {template_id} details for user {current_user.email}")
        
        return template
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving template {template_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve template details"
        )


@router.get("/{template_id}/preview")
async def generate_template_preview(
    template_id: str,
    format: str = Query(default="png", regex="^(png|jpg|jpeg)$", description="Image format"),
    width: int = Query(default=400, ge=100, le=1200, description="Preview width in pixels"),
    height: int = Query(default=600, ge=150, le=1800, description="Preview height in pixels"),
    primary_color: str = Query(default=None, regex="^#[0-9A-Fa-f]{6}$", description="Primary color override"),
    current_user: User = Depends(get_current_user)
):
    """
    Generate a preview image of the template with sample data.
    
    Creates a visual representation of the template layout and styling
    that can be used for template selection in the frontend.
    
    Args:
        template_id: Template unique identifier
        format: Image format (png, jpg, jpeg)
        width: Preview width in pixels (100-1200)
        height: Preview height in pixels (150-1800)
        primary_color: Primary color override (hex format)
        current_user: Current authenticated user
        
    Returns:
        Response: Binary image data with appropriate content type
        
    Raises:
        HTTPException: If template not found or preview generation fails
    """
    try:
        # Check if template exists
        if not template_registry.template_exists(template_id):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Template '{template_id}' not found"
            )
        
        # Generate preview image
        image_data = template_preview_service.generate_preview(
            template_id=template_id,
            format=format,
            width=width,
            height=height,
            primary_color=primary_color
        )
        
        if not image_data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate template preview"
            )
        
        # Determine content type
        content_type = f"image/{format.lower()}"
        if format.lower() == "jpg":
            content_type = "image/jpeg"
        
        logger.debug(f"Generated preview for template {template_id} (user: {current_user.email})")
        
        return Response(
            content=image_data,
            media_type=content_type,
            headers={
                "Content-Length": str(len(image_data)),
                "Cache-Control": "public, max-age=3600"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating preview for template {template_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate template preview"
        )
