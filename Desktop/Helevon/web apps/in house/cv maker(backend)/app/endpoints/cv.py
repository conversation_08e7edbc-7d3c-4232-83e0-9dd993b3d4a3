"""
CV management endpoints.

This module contains all CV-related API endpoints including
CRUD operations and CV section management.
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from loguru import logger

from app.core.database import get_db
from app.core.dependencies import get_current_user, require_user_ownership
from app.core.config import settings
from app.models.user import User
from app.models.cv import CV
from app.models.user_activity import UserActivity
from app.schemas.cv import (
    CVCreate, CVUpdate, CVResponse, CVWithFiles, CVListResponse,
    PersonalInfoUpdate, EducationUpdate, WorkExperienceUpdate,
    SkillsUpdate, ReferencesUpdate, CoverLetterSectionUpdate
)
from app.schemas.base import SuccessResponse


router = APIRouter()


@router.post("", response_model=CVResponse, status_code=status.HTTP_201_CREATED)
async def create_cv(
    cv_data: CVCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new CV.
    
    Args:
        cv_data: CV creation data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        CVResponse: Created CV information
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        # Ensure user can only create CVs for themselves
        if cv_data.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot create CV for another user"
            )
        
        # Create new CV
        new_cv = CV(
            user_id=current_user.id,
            title=cv_data.title,
            template=cv_data.template,
            language=cv_data.language
        )
        
        # Initialize empty sections
        new_cv.initialize_empty_sections()
        
        db.add(new_cv)
        await db.commit()
        await db.refresh(new_cv)
        
        # Log CV creation activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="create_cv",
            endpoint="/api/v1/cv",
            details={
                "cv_id": new_cv.id,
                "title": new_cv.title,
                "template": new_cv.template,
                "language": new_cv.language
            }
        )
        db.add(activity)
        await db.commit()
        
        logger.info(f"CV created: {new_cv.id} by user {current_user.email}")
        return CVResponse.model_validate(new_cv)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"CV creation error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create CV"
        )


@router.get("", response_model=List[CVListResponse])
async def get_user_cvs(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all CVs for the authenticated user.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[CVListResponse]: List of user's CVs
    """
    try:
        result = await db.execute(
            select(CV).where(CV.user_id == current_user.id).order_by(CV.updated_at.desc())
        )
        cvs = result.scalars().all()
        
        logger.debug(f"Retrieved {len(cvs)} CVs for user {current_user.email}")
        return [CVListResponse.model_validate(cv) for cv in cvs]
        
    except Exception as e:
        logger.error(f"Error retrieving CVs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve CVs"
        )


@router.get("/{cv_id}", response_model=CVWithFiles)
async def get_cv(
    cv_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific CV by ID.
    
    Args:
        cv_id: CV unique identifier
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        CVWithFiles: CV with associated files
        
    Raises:
        HTTPException: If CV not found or access denied
    """
    try:
        # Get CV with files
        result = await db.execute(
            select(CV).options(selectinload(CV.files)).where(CV.id == cv_id)
        )
        cv = result.scalar_one_or_none()
        
        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )
        
        # Check ownership
        require_user_ownership(cv.user_id, current_user)
        
        logger.debug(f"Retrieved CV {cv_id} for user {current_user.email}")
        
        # Convert to response format
        cv_dict = cv.__dict__.copy()
        cv_dict['files'] = [
            {
                "id": file.id,
                "name": file.name,
                "url": file.url,
                "category": file.category
            }
            for file in cv.files
        ]
        
        return CVWithFiles.model_validate(cv_dict)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving CV {cv_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve CV"
        )


@router.put("/{cv_id}", response_model=CVResponse)
async def update_cv(
    cv_id: str,
    cv_update: CVUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a CV.
    
    Args:
        cv_id: CV unique identifier
        cv_update: CV update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        CVResponse: Updated CV information
        
    Raises:
        HTTPException: If CV not found or access denied
    """
    try:
        # Get CV
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()
        
        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )
        
        # Check ownership
        require_user_ownership(cv.user_id, current_user)
        
        # Track updated fields
        updated_fields = []
        
        # Update fields
        if cv_update.title is not None:
            cv.title = cv_update.title
            updated_fields.append("title")
        
        if cv_update.template is not None:
            cv.template = cv_update.template
            updated_fields.append("template")
        
        if cv_update.language is not None:
            cv.language = cv_update.language
            updated_fields.append("language")
        
        await db.commit()
        await db.refresh(cv)
        
        # Log CV update activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="update_cv",
            endpoint=f"/api/v1/cv/{cv_id}",
            details={
                "cv_id": cv.id,
                "updated_fields": updated_fields
            }
        )
        db.add(activity)
        await db.commit()
        
        logger.info(f"CV updated: {cv_id} by user {current_user.email}")
        return CVResponse.model_validate(cv)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"CV update error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update CV"
        )


@router.delete("/{cv_id}", response_model=SuccessResponse)
async def delete_cv(
    cv_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a CV.
    
    Args:
        cv_id: CV unique identifier
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        SuccessResponse: Deletion confirmation
        
    Raises:
        HTTPException: If CV not found or access denied
    """
    try:
        # Get CV
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()
        
        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )
        
        # Check ownership
        require_user_ownership(cv.user_id, current_user)
        
        cv_title = cv.title
        
        # Delete CV (cascade will handle related data)
        await db.delete(cv)
        await db.commit()
        
        # Log CV deletion activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="delete_cv",
            endpoint=f"/api/v1/cv/{cv_id}",
            details={
                "cv_id": cv_id,
                "title": cv_title
            }
        )
        db.add(activity)
        await db.commit()
        
        logger.info(f"CV deleted: {cv_id} by user {current_user.email}")
        return SuccessResponse(message="CV deleted successfully")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"CV deletion error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete CV"
        )


@router.put("/{cv_id}/personal-info", response_model=CVResponse)
async def update_personal_info(
    cv_id: str,
    personal_info: PersonalInfoUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update personal information section of a CV.

    Args:
        cv_id: CV unique identifier
        personal_info: Personal information update data
        current_user: Current authenticated user
        db: Database session

    Returns:
        CVResponse: Updated CV information

    Raises:
        HTTPException: If CV not found or access denied
    """
    try:
        # Get CV
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()

        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )

        # Check ownership
        require_user_ownership(cv.user_id, current_user)

        # Update personal info with only provided fields
        current_info = cv.personal_info or {}
        update_data = personal_info.model_dump(exclude_unset=True)

        # Remove photoUrl from update data - it should be managed separately via file upload
        if 'photoUrl' in update_data:
            del update_data['photoUrl']

        current_info.update(update_data)

        # Force SQLAlchemy to detect the change by creating a new dict
        cv.personal_info = dict(current_info)

        # Mark the field as modified to ensure SQLAlchemy updates it
        from sqlalchemy.orm import attributes
        attributes.flag_modified(cv, 'personal_info')

        await db.commit()
        await db.refresh(cv)

        # Log activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="update_personal_info",
            endpoint=f"/api/v1/cv/{cv_id}/personal-info",
            details={"cv_id": cv.id, "updated_fields": list(update_data.keys())}
        )
        db.add(activity)
        await db.commit()

        logger.info(f"Personal info updated for CV {cv_id} by user {current_user.email}")
        return CVResponse.model_validate(cv)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Personal info update error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update personal information"
        )


@router.put("/{cv_id}/education", response_model=CVResponse)
async def update_education(
    cv_id: str,
    education_data: EducationUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update education section of a CV.

    Args:
        cv_id: CV unique identifier
        education_data: Education update data
        current_user: Current authenticated user
        db: Database session

    Returns:
        CVResponse: Updated CV information
    """
    try:
        # Get CV
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()

        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )

        # Check ownership
        require_user_ownership(cv.user_id, current_user)

        # Update education section
        cv.education = [entry.model_dump() for entry in education_data.education]

        await db.commit()
        await db.refresh(cv)

        # Log activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="update_education",
            endpoint=f"/api/v1/cv/{cv_id}/education",
            details={"cv_id": cv.id, "entries_count": len(education_data.education)}
        )
        db.add(activity)
        await db.commit()

        logger.info(f"Education updated for CV {cv_id} by user {current_user.email}")
        return CVResponse.model_validate(cv)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Education update error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update education"
        )


@router.put("/{cv_id}/work-experience", response_model=CVResponse)
async def update_work_experience(
    cv_id: str,
    work_data: WorkExperienceUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update work experience section of a CV.

    Args:
        cv_id: CV unique identifier
        work_data: Work experience update data
        current_user: Current authenticated user
        db: Database session

    Returns:
        CVResponse: Updated CV information
    """
    try:
        # Get CV
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()

        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )

        # Check ownership
        require_user_ownership(cv.user_id, current_user)

        # Update work experience section
        cv.work_experience = [entry.model_dump() for entry in work_data.workExperience]

        await db.commit()
        await db.refresh(cv)

        # Log activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="update_work_experience",
            endpoint=f"/api/v1/cv/{cv_id}/work-experience",
            details={"cv_id": cv.id, "entries_count": len(work_data.workExperience)}
        )
        db.add(activity)
        await db.commit()

        logger.info(f"Work experience updated for CV {cv_id} by user {current_user.email}")
        return CVResponse.model_validate(cv)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Work experience update error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update work experience"
        )


@router.put("/{cv_id}/skills", response_model=CVResponse)
async def update_skills(
    cv_id: str,
    skills_data: SkillsUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update skills section of a CV.

    Args:
        cv_id: CV unique identifier
        skills_data: Skills update data
        current_user: Current authenticated user
        db: Database session

    Returns:
        CVResponse: Updated CV information
    """
    try:
        # Get CV
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()

        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )

        # Check ownership
        require_user_ownership(cv.user_id, current_user)

        # Update skills section with auto-generated IDs
        import uuid
        skills_list = []
        for skill in skills_data.skills:
            skill_dict = skill.model_dump()
            # Auto-generate ID if not provided
            if not skill_dict.get('id'):
                skill_dict['id'] = str(uuid.uuid4())
            skills_list.append(skill_dict)

        cv.skills = skills_list

        # Force SQLAlchemy to detect the change
        from sqlalchemy.orm import attributes
        attributes.flag_modified(cv, 'skills')

        await db.commit()
        await db.refresh(cv)

        # Log activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="update_skills",
            endpoint=f"/api/v1/cv/{cv_id}/skills",
            details={"cv_id": cv.id, "entries_count": len(skills_data.skills)}
        )
        db.add(activity)
        await db.commit()

        logger.info(f"Skills updated for CV {cv_id} by user {current_user.email}")
        return CVResponse.model_validate(cv)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Skills update error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update skills"
        )


@router.put("/{cv_id}/references", response_model=CVResponse)
async def update_references(
    cv_id: str,
    references_data: ReferencesUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update references section of a CV.

    Args:
        cv_id: CV unique identifier
        references_data: References update data
        current_user: Current authenticated user
        db: Database session

    Returns:
        CVResponse: Updated CV information
    """
    try:
        # Get CV
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()

        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )

        # Check ownership
        require_user_ownership(cv.user_id, current_user)

        # Update references section
        cv.references = [ref.model_dump() for ref in references_data.references]

        await db.commit()
        await db.refresh(cv)

        # Log activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="update_references",
            endpoint=f"/api/v1/cv/{cv_id}/references",
            details={"cv_id": cv.id, "entries_count": len(references_data.references)}
        )
        db.add(activity)
        await db.commit()

        logger.info(f"References updated for CV {cv_id} by user {current_user.email}")
        return CVResponse.model_validate(cv)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"References update error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update references"
        )


@router.put("/{cv_id}/cover-letter", response_model=CVResponse)
async def update_cover_letter(
    cv_id: str,
    cover_letter_data: CoverLetterSectionUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update cover letter section of a CV.

    Args:
        cv_id: CV unique identifier
        cover_letter_data: Cover letter update data
        current_user: Current authenticated user
        db: Database session

    Returns:
        CVResponse: Updated CV information
    """
    try:
        # Get CV
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()

        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )

        # Check ownership
        require_user_ownership(cv.user_id, current_user)

        # Update cover letter with new structured format
        import json
        from datetime import datetime

        cover_letter_dict = cover_letter_data.coverLetter.model_dump()

        # Set default date if not provided
        if not cover_letter_dict.get('date'):
            cover_letter_dict['date'] = datetime.now().strftime('%Y-%m-%d')

        # Set default subject if not provided (use CV title)
        if not cover_letter_dict.get('subject') and cv.title:
            cover_letter_dict['subject'] = f"Bewerbung - {cv.title}"

        # Store as JSON in the database
        cv.cover_letter = json.dumps(cover_letter_dict)

        # Force SQLAlchemy to detect the change
        from sqlalchemy.orm import attributes
        attributes.flag_modified(cv, 'cover_letter')

        await db.commit()
        await db.refresh(cv)

        # Log activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="update_cover_letter",
            endpoint=f"/api/v1/cv/{cv_id}/cover-letter",
            details={"cv_id": cv.id}
        )
        db.add(activity)
        await db.commit()

        logger.info(f"Cover letter updated for CV {cv_id} by user {current_user.email}")
        return CVResponse.model_validate(cv)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Cover letter update error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update cover letter"
        )


@router.get("/{cv_id}/export")
async def export_cv_pdf(
    cv_id: str,
    template_id: str = Query(None, description="Template ID override"),
    include_certificates: bool = Query(True, description="Include certificate attachments"),
    include_cover_letter: bool = Query(True, description="Include cover letter"),
    primary_color: str = Query(None, regex="^#[0-9A-Fa-f]{6}$", description="Primary color override"),
    format: str = Query("pdf", description="Export format"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Generate and download CV as PDF with enhanced template support.

    Args:
        cv_id: CV unique identifier
        template_id: Template ID override (uses CV template if not provided)
        include_certificates: Include certificate attachments
        include_cover_letter: Include cover letter
        primary_color: Primary color override (hex format)
        format: Export format (default: pdf)
        current_user: Current authenticated user
        db: Database session

    Returns:
        Response: PDF file with Content-Type: application/pdf

    Raises:
        HTTPException: If CV not found, access denied, template invalid, or export disabled
    """
    from app.services.pdf_service import PDFService
    from fastapi.responses import Response

    try:
        # Check if PDF export is enabled
        if not settings.CAN_EXPORT:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="PDF export is currently disabled"
            )

        # Get CV with files
        result = await db.execute(
            select(CV).options(selectinload(CV.files)).where(CV.id == cv_id)
        )
        cv = result.scalar_one_or_none()

        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )

        # Check ownership
        require_user_ownership(cv.user_id, current_user)

        # Validate template if provided
        if template_id:
            from app.services.template_service import template_registry
            if not template_registry.template_exists(template_id):
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail=f"Template '{template_id}' not found"
                )

        # Generate PDF with enhanced options
        pdf_service = PDFService()
        pdf_data = await pdf_service.generate_cv_pdf(
            cv=cv,
            db=db,
            template_override=template_id,
            include_certificates=include_certificates,
            include_cover_letter=include_cover_letter,
            primary_color=primary_color
        )

        # Log PDF export activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="export_pdf",
            endpoint=f"/api/v1/cv/{cv_id}/export",
            details={
                "cv_id": cv.id,
                "format": format,
                "template": cv.template,
                "language": cv.language
            }
        )
        db.add(activity)
        await db.commit()

        logger.info(f"PDF exported for CV {cv_id} by user {current_user.email}")

        # Return PDF response
        filename = f"{cv.title.replace(' ', '_')}_CV.pdf"
        return Response(
            content=pdf_data,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"PDF export error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export PDF"
        )
