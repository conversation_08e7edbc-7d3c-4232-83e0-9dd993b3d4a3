"""
File management endpoints.

This module contains all file-related API endpoints including
file upload, retrieval, and management operations.
"""

import base64
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from fastapi.responses import Response
from fastapi.responses import Response
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from loguru import logger

from app.core.database import get_db
from app.core.config import settings
from app.core.dependencies import get_current_user, require_user_ownership
from app.models.user import User
from app.models.cv import CV
from app.models.file import File as FileModel
from app.models.user_activity import UserActivity
from app.schemas.file import (
    FileResponse, FileWithData, FileListResponse, FileDeleteResponse
)
from app.schemas.base import SuccessResponse
from app.services.file_validation_service import file_validation_service


router = APIRouter()


@router.post("/cv/{cv_id}/photo", response_model=FileResponse, status_code=status.HTTP_201_CREATED)
async def upload_profile_photo(
    cv_id: str,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Upload profile photo for a CV.

    Args:
        cv_id: CV unique identifier
        file: Photo file to upload (JPG, PNG only)
        current_user: Current authenticated user
        db: Database session

    Returns:
        FileResponse: Uploaded photo information

    Raises:
        HTTPException: If upload fails or validation errors occur
    """
    try:
        # Get CV and verify ownership
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()

        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )

        require_user_ownership(cv.user_id, current_user)

        # Read file content
        file_content = await file.read()

        # Validate photo file (JPG/PNG only)
        is_valid, error_message, validation_info = file_validation_service.validate_file(
            file_content=file_content,
            filename=file.filename,
            declared_mime_type=file.content_type,
            category="photo"
        )

        if not is_valid:
            logger.warning(f"Photo validation failed: {error_message}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_message
            )

        # Check if it's actually an image
        if not validation_info['actual_mime_type'].startswith('image/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Profile photo must be an image file (JPG or PNG)"
            )

        # Use validated information
        file_size = validation_info['file_size']
        safe_filename = validation_info['sanitized_filename']
        safe_mime_type = file_validation_service.get_safe_mime_type(validation_info)

        # Encode file data
        file_data = base64.b64encode(file_content).decode('utf-8')

        # Delete existing profile photo if exists
        existing_photo = await db.execute(
            select(FileModel).where(
                and_(
                    FileModel.cv_id == cv_id,
                    FileModel.category == "photo",
                    FileModel.user_id == current_user.id
                )
            )
        )
        existing_photo = existing_photo.scalar_one_or_none()

        if existing_photo:
            await db.delete(existing_photo)

        # Create new photo record
        new_photo = FileModel(
            user_id=current_user.id,
            cv_id=cv_id,
            name=safe_filename,
            type=safe_mime_type,
            size=file_size,
            file_data=file_data,
            category="photo",
            url=""  # Not used for direct file storage
        )

        db.add(new_photo)
        await db.commit()
        await db.refresh(new_photo)

        # Update CV personal_info with photo ID
        personal_info = cv.personal_info or {}
        personal_info['photoUrl'] = new_photo.id
        cv.personal_info = personal_info

        await db.commit()

        logger.info(f"Profile photo uploaded for CV {cv_id}: {safe_filename}")

        return FileResponse(
            id=new_photo.id,
            name=new_photo.name,
            type=new_photo.type,
            size=new_photo.size,
            category=new_photo.category,
            url=f"/api/v1/files/{new_photo.id}",
            created_at=new_photo.created_at,
            updated_at=new_photo.updated_at
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Profile photo upload failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload profile photo"
        )


@router.post("/cv/{cv_id}/education/{education_id}/certificate", response_model=FileResponse, status_code=status.HTTP_201_CREATED)
async def upload_certificate(
    cv_id: str,
    education_id: str,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Upload certificate for an education entry.

    Args:
        cv_id: CV unique identifier
        education_id: Education entry identifier
        file: Certificate file to upload (PDF, JPG, PNG only)
        current_user: Current authenticated user
        db: Database session

    Returns:
        FileResponse: Uploaded certificate information

    Raises:
        HTTPException: If upload fails or validation errors occur
    """
    try:
        # Get CV and verify ownership
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()

        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )

        require_user_ownership(cv.user_id, current_user)

        # Find education entry
        education_entry = None
        for edu in cv.education or []:
            if edu.get('id') == education_id:
                education_entry = edu
                break

        if not education_entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Education entry not found"
            )

        # Read file content
        file_content = await file.read()

        # Validate certificate file (PDF, JPG, PNG only)
        is_valid, error_message, validation_info = file_validation_service.validate_file(
            file_content=file_content,
            filename=file.filename,
            declared_mime_type=file.content_type,
            category="certificate"
        )

        if not is_valid:
            logger.warning(f"Certificate validation failed: {error_message}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_message
            )

        # Check if it's a valid certificate format
        actual_mime = validation_info['actual_mime_type']
        if not (actual_mime == 'application/pdf' or actual_mime.startswith('image/')):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Certificate must be a PDF or image file (JPG, PNG)"
            )

        # Use validated information
        file_size = validation_info['file_size']
        safe_filename = validation_info['sanitized_filename']
        safe_mime_type = file_validation_service.get_safe_mime_type(validation_info)

        # Encode file data
        file_data = base64.b64encode(file_content).decode('utf-8')

        # Create certificate record
        new_certificate = FileModel(
            user_id=current_user.id,
            cv_id=cv_id,
            name=safe_filename,
            type=safe_mime_type,
            size=file_size,
            file_data=file_data,
            category="certificate",
            url=""  # Not used for direct file storage
        )

        db.add(new_certificate)
        await db.commit()
        await db.refresh(new_certificate)

        # Update education entry with certificate ID
        if 'certificates' not in education_entry:
            education_entry['certificates'] = []

        education_entry['certificates'].append(new_certificate.id)

        # Update CV with modified education data
        cv.education = cv.education  # Trigger SQLAlchemy update
        await db.commit()

        logger.info(f"Certificate uploaded for education {education_id}: {safe_filename}")

        return FileResponse(
            id=new_certificate.id,
            name=new_certificate.name,
            type=new_certificate.type,
            size=new_certificate.size,
            category=new_certificate.category,
            url=f"/api/v1/files/{new_certificate.id}",
            created_at=new_certificate.created_at,
            updated_at=new_certificate.updated_at
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Certificate upload failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload certificate"
        )


@router.get("/files/{file_id}")
async def get_file(
    file_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Retrieve a file by ID (returns the actual file content).

    Args:
        file_id: File unique identifier
        current_user: Current authenticated user
        db: Database session

    Returns:
        Response: File content with appropriate headers

    Raises:
        HTTPException: If file not found or access denied
    """
    try:
        # Get file record
        result = await db.execute(select(FileModel).where(FileModel.id == file_id))
        file_record = result.scalar_one_or_none()

        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        # Verify ownership
        require_user_ownership(file_record.user_id, current_user)

        # Decode file data
        try:
            file_content = base64.b64decode(file_record.file_data)
        except Exception as e:
            logger.error(f"Failed to decode file data for {file_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="File data corrupted"
            )

        # Return file with appropriate headers
        return Response(
            content=file_content,
            media_type=file_record.type,
            headers={
                "Content-Disposition": f"inline; filename=\"{file_record.name}\"",
                "Content-Length": str(len(file_content)),
                "Cache-Control": "private, max-age=3600"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"File retrieval failed for {file_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve file"
        )


@router.delete("/files/{file_id}", response_model=FileDeleteResponse)
async def delete_file(
    file_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a file by ID.

    Args:
        file_id: File unique identifier
        current_user: Current authenticated user
        db: Database session

    Returns:
        FileDeleteResponse: Deletion confirmation

    Raises:
        HTTPException: If file not found or access denied
    """
    try:
        # Get file record
        result = await db.execute(select(FileModel).where(FileModel.id == file_id))
        file_record = result.scalar_one_or_none()

        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        # Verify ownership
        require_user_ownership(file_record.user_id, current_user)

        # If it's a profile photo, remove from CV personal_info
        if file_record.category == "photo":
            cv_result = await db.execute(select(CV).where(CV.id == file_record.cv_id))
            cv = cv_result.scalar_one_or_none()

            if cv and cv.personal_info and cv.personal_info.get('photoUrl') == file_id:
                personal_info = cv.personal_info.copy()
                personal_info['photoUrl'] = None
                cv.personal_info = personal_info

        # If it's a certificate, remove from education entry
        elif file_record.category == "certificate":
            cv_result = await db.execute(select(CV).where(CV.id == file_record.cv_id))
            cv = cv_result.scalar_one_or_none()

            if cv and cv.education:
                for edu in cv.education:
                    if 'certificates' in edu and file_id in edu['certificates']:
                        edu['certificates'].remove(file_id)
                        break
                cv.education = cv.education  # Trigger SQLAlchemy update

        # Delete file record
        await db.delete(file_record)
        await db.commit()

        logger.info(f"File deleted: {file_id}")

        return FileDeleteResponse(
            message="File deleted successfully",
            file_id=file_id
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"File deletion failed for {file_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete file"
        )


@router.post("/cv/{cv_id}/upload", response_model=FileResponse, status_code=status.HTTP_201_CREATED)
async def upload_file(
    cv_id: str,
    file: UploadFile = File(...),
    category: str = Form(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Upload a file associated with a CV.
    
    Args:
        cv_id: CV unique identifier
        file: File to upload
        category: File category (photo, certificate, cover_letter, other)
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        FileResponse: Uploaded file information
        
    Raises:
        HTTPException: If upload fails or validation errors occur
    """
    try:
        # Validate category
        valid_categories = ["photo", "certificate", "cover_letter", "other"]
        if category not in valid_categories:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid category. Must be one of: {', '.join(valid_categories)}"
            )
        
        # Get CV and verify ownership
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()
        
        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )
        
        require_user_ownership(cv.user_id, current_user)
        
        # Read file content
        file_content = await file.read()

        # Comprehensive file validation
        is_valid, error_message, validation_info = file_validation_service.validate_file(
            file_content=file_content,
            filename=file.filename,
            declared_mime_type=file.content_type,
            category=category
        )

        if not is_valid:
            logger.warning(f"File validation failed: {error_message} - {validation_info}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_message
            )

        # Use validated information
        file_size = validation_info['file_size']
        safe_filename = validation_info['sanitized_filename']
        safe_mime_type = file_validation_service.get_safe_mime_type(validation_info)
        
        # Encode file as base64
        file_data = base64.b64encode(file_content).decode('utf-8')
        
        # Handle single photo per CV constraint
        if category == "photo":
            # Delete existing photo for this CV
            existing_photo = await db.execute(
                select(FileModel).where(
                    FileModel.cv_id == cv_id,
                    FileModel.category == "photo"
                )
            )
            existing = existing_photo.scalar_one_or_none()
            if existing:
                await db.delete(existing)
        
        # Create file record with validated information
        new_file = FileModel(
            user_id=current_user.id,
            cv_id=cv_id,
            name=safe_filename,
            type=safe_mime_type,
            size=file_size,
            file_data=file_data,
            category=category,
            url=""  # Will be set after creation
        )
        
        db.add(new_file)
        await db.commit()
        await db.refresh(new_file)
        
        # Generate URL
        new_file.url = new_file.generate_url()
        await db.commit()
        await db.refresh(new_file)
        
        # Log file upload activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="upload_file",
            endpoint=f"/api/v1/cv/{cv_id}/upload",
            details={
                "file_id": new_file.id,
                "cv_id": cv_id,
                "filename": file.filename,
                "category": category,
                "size": file_size
            }
        )
        db.add(activity)
        await db.commit()
        
        logger.info(f"File uploaded: {new_file.id} for CV {cv_id} by user {current_user.email}")
        return FileResponse.model_validate(new_file)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"File upload error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="File upload failed"
        )


@router.get("/cv/{cv_id}/file/{file_id}")
async def get_file(
    cv_id: str,
    file_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Retrieve a file by ID.
    
    Args:
        cv_id: CV unique identifier
        file_id: File unique identifier
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Response: Binary file data with appropriate Content-Type header
        
    Raises:
        HTTPException: If file not found or access denied
    """
    try:
        # Get file
        result = await db.execute(
            select(FileModel).where(
                FileModel.id == file_id,
                FileModel.cv_id == cv_id
            )
        )
        file_record = result.scalar_one_or_none()
        
        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        # Check ownership
        require_user_ownership(file_record.user_id, current_user)
        
        # Decode base64 file data
        file_data = base64.b64decode(file_record.file_data)
        
        logger.debug(f"File retrieved: {file_id} by user {current_user.email}")
        
        return Response(
            content=file_data,
            media_type=file_record.type,
            headers={
                "Content-Disposition": f"inline; filename={file_record.name}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"File retrieval error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve file"
        )


@router.get("/cv/{cv_id}/files", response_model=List[FileListResponse])
async def get_cv_files(
    cv_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all files associated with a CV.
    
    Args:
        cv_id: CV unique identifier
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[FileListResponse]: List of files
    """
    try:
        # Verify CV ownership
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()
        
        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )
        
        require_user_ownership(cv.user_id, current_user)
        
        # Get files
        files_result = await db.execute(
            select(FileModel).where(FileModel.cv_id == cv_id).order_by(FileModel.created_at.desc())
        )
        files = files_result.scalars().all()
        
        logger.debug(f"Retrieved {len(files)} files for CV {cv_id}")
        
        return [
            FileListResponse(
                id=file.id,
                name=file.name,
                type=file.type,
                size=file.size,
                category=file.category,
                url=file.url,
                created_at=file.created_at.isoformat()
            )
            for file in files
        ]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving CV files: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve files"
        )


@router.delete("/cv/{cv_id}/file/{file_id}", response_model=FileDeleteResponse)
async def delete_file(
    cv_id: str,
    file_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a file.

    Args:
        cv_id: CV unique identifier
        file_id: File unique identifier
        current_user: Current authenticated user
        db: Database session

    Returns:
        FileDeleteResponse: Deletion confirmation

    Raises:
        HTTPException: If file not found or access denied
    """
    try:
        # Get file
        result = await db.execute(
            select(FileModel).where(
                FileModel.id == file_id,
                FileModel.cv_id == cv_id
            )
        )
        file_record = result.scalar_one_or_none()

        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        # Check ownership
        require_user_ownership(file_record.user_id, current_user)

        file_name = file_record.name

        # Delete file
        await db.delete(file_record)
        await db.commit()

        # Log file deletion activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="delete_file",
            endpoint=f"/api/v1/cv/{cv_id}/file/{file_id}",
            details={
                "file_id": file_id,
                "cv_id": cv_id,
                "filename": file_name
            }
        )
        db.add(activity)
        await db.commit()

        logger.info(f"File deleted: {file_id} by user {current_user.email}")
        return FileDeleteResponse(
            message="File deleted successfully",
            file_id=file_id
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"File deletion error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete file"
        )


@router.get("/file/{file_id}")
async def get_standalone_file(
    file_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Retrieve a standalone file by ID (not associated with CV).

    Args:
        file_id: File unique identifier
        current_user: Current authenticated user
        db: Database session

    Returns:
        Response: Binary file data with appropriate Content-Type header
    """
    try:
        # Get file
        result = await db.execute(
            select(FileModel).where(
                FileModel.id == file_id,
                FileModel.cv_id.is_(None)
            )
        )
        file_record = result.scalar_one_or_none()

        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        # Check ownership
        require_user_ownership(file_record.user_id, current_user)

        # Decode base64 file data
        file_data = base64.b64decode(file_record.file_data)

        logger.debug(f"Standalone file retrieved: {file_id} by user {current_user.email}")

        return Response(
            content=file_data,
            media_type=file_record.type,
            headers={
                "Content-Disposition": f"inline; filename={file_record.name}"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Standalone file retrieval error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve file"
        )
