# Application Settings
DEBUG=true
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database Settings
DATABASE_URL=sqlite+aiosqlite:///./cv_maker.db
DATABASE_URL_PROD=postgresql+asyncpg://user:password@localhost:5432/cvmaker

# CORS Settings
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8000,http://127.0.0.1:3000

# File Upload Settings
MAX_UPLOAD_SIZE_MB=5
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/jpg,application/pdf

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# PDF Export
CAN_EXPORT=true

# Server Settings
HOST=0.0.0.0
PORT=8000
WORKERS=4
