#!/usr/bin/env python3
"""
Test PDF generation with the new file management system.

This script tests that photos and certificates are properly displayed
in the generated PDF using the new binary file storage system.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select

from app.core.config import settings
from app.models.cv import CV
from app.models.user import User
from app.services.pdf_service import PDFService
from loguru import logger


async def test_pdf_generation():
    """Test PDF generation with photos and certificates."""
    logger.info("🧪 Testing PDF generation with new file management system...")
    
    # Setup database connection
    db_url = settings.get_database_url()
    engine = create_async_engine(db_url, echo=False)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    try:
        async with async_session() as session:
            # Get the sample user and CV
            result = await session.execute(
                select(User).where(User.email == "<EMAIL>")
            )
            user = result.scalar_one_or_none()
            
            if not user:
                logger.error("❌ Sample user not found. Run the migration script first.")
                return False
            
            result = await session.execute(
                select(CV).where(CV.user_id == user.id)
            )
            cv = result.scalar_one_or_none()
            
            if not cv:
                logger.error("❌ Sample CV not found. Run the migration script first.")
                return False
            
            logger.info(f"✅ Found CV: {cv.title}")
            logger.info(f"   📸 Photo URL: {cv.personal_info.get('photoUrl')}")
            
            # Check education certificates
            cert_count = 0
            for edu in cv.education:
                certs = edu.get('certificates', [])
                cert_count += len(certs)
                if certs:
                    logger.info(f"   📜 {edu['institution']}: {len(certs)} certificate(s)")
            
            logger.info(f"   📊 Total certificates: {cert_count}")
            
            # Test PDF generation for all templates
            pdf_service = PDFService()
            templates = ["standard", "modern", "german-ausbildung"]
            
            for template in templates:
                logger.info(f"\n🔄 Testing {template} template...")
                
                # Temporarily set the template
                original_template = cv.template
                cv.template = template
                
                try:
                    # Generate PDF
                    pdf_data = await pdf_service.generate_cv_pdf(
                        cv=cv,
                        db=session,
                        template_override=template,
                        include_certificates=True,
                        include_cover_letter=True,
                        primary_color="#005A9C"
                    )
                    
                    # Save PDF to file for inspection
                    output_file = f"test_output_{template}.pdf"
                    with open(output_file, 'wb') as f:
                        f.write(pdf_data)
                    
                    logger.info(f"✅ {template} PDF generated successfully: {output_file}")
                    logger.info(f"   📏 Size: {len(pdf_data):,} bytes")
                    
                except Exception as e:
                    logger.error(f"❌ {template} PDF generation failed: {e}")
                    return False
                finally:
                    # Restore original template
                    cv.template = original_template
            
            # Test filename generation
            logger.info(f"\n🔄 Testing filename generation...")
            
            personal_info = cv.personal_info or {}
            first_name = personal_info.get('firstName', 'CV')
            last_name = personal_info.get('lastName', 'Export')
            cv_title = cv.title.replace(' ', '_') if cv.title else 'CV'
            
            # Sanitize filename components
            import re
            first_name = re.sub(r'[^\w\s-]', '', first_name).strip()
            last_name = re.sub(r'[^\w\s-]', '', last_name).strip()
            cv_title = re.sub(r'[^\w\s-]', '', cv_title).strip()
            
            filename = f"{first_name} {last_name} - {cv_title}.pdf"
            logger.info(f"✅ Generated filename: {filename}")
            
            # Test file retrieval directly
            logger.info(f"\n🔄 Testing direct file retrieval...")
            
            from app.models.file import File
            
            # Test photo retrieval
            photo_id = cv.personal_info.get('photoUrl')
            if photo_id:
                photo = await session.get(File, photo_id)
                if photo:
                    logger.info(f"✅ Photo retrieved: {photo.name} ({len(photo.file_data)} bytes)")
                    assert isinstance(photo.file_data, bytes), "Photo data should be bytes"
                else:
                    logger.error(f"❌ Photo not found: {photo_id}")
                    return False
            
            # Test certificate retrieval
            for edu in cv.education:
                for cert_id in edu.get('certificates', []):
                    cert = await session.get(File, cert_id)
                    if cert:
                        logger.info(f"✅ Certificate retrieved: {cert.name} ({len(cert.file_data)} bytes)")
                        assert isinstance(cert.file_data, bytes), "Certificate data should be bytes"
                        assert cert.education_entry_id == edu['id'], "Certificate should be linked to education entry"
                    else:
                        logger.error(f"❌ Certificate not found: {cert_id}")
                        return False
            
            # Test standalone certificates
            result = await session.execute(
                select(File).where(
                    File.cv_id == cv.id,
                    File.category == 'certificate',
                    File.education_entry_id.is_(None)
                )
            )
            standalone_certs = result.scalars().all()
            
            for cert in standalone_certs:
                logger.info(f"✅ Standalone certificate: {cert.name}")
                logger.info(f"   📝 Description: {cert.file_description}")
                logger.info(f"   🏢 Issuer: {cert.issuing_organization}")
                logger.info(f"   📅 Issue Date: {cert.issue_date}")
            
            logger.info(f"\n🎉 All tests passed successfully!")
            logger.info(f"📊 Summary:")
            logger.info(f"   👤 User: {user.email}")
            logger.info(f"   📄 CV: {cv.title}")
            logger.info(f"   📸 Photo: {'✅ Linked' if cv.personal_info.get('photoUrl') else '❌ Missing'}")
            logger.info(f"   📜 Education certificates: {sum(len(edu.get('certificates', [])) for edu in cv.education)}")
            logger.info(f"   📜 Standalone certificates: {len(standalone_certs)}")
            logger.info(f"   📁 PDF files generated: {len(templates)}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        await engine.dispose()


async def test_pdf_service_methods():
    """Test specific PDF service methods for file handling."""
    logger.info("\n🧪 Testing PDF service file handling methods...")
    
    db_url = settings.get_database_url()
    engine = create_async_engine(db_url, echo=False)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    try:
        async with async_session() as session:
            # Get sample CV
            result = await session.execute(
                select(CV).where(CV.title == "Senior Software Developer CV")
            )
            cv = result.scalar_one_or_none()
            
            if not cv:
                logger.error("❌ Sample CV not found")
                return False
            
            pdf_service = PDFService()
            
            # Test photo retrieval method
            logger.info("🔄 Testing photo retrieval...")
            photo_id = cv.personal_info.get('photoUrl')
            if photo_id:
                from app.models.file import File
                result = await session.execute(
                    select(File).where(File.id == photo_id)
                )
                photo_file = result.scalar_one_or_none()
                
                if photo_file and photo_file.file_data:
                    logger.info(f"✅ Photo file found: {photo_file.name}")
                    
                    # Test binary data handling
                    if isinstance(photo_file.file_data, bytes):
                        logger.info("✅ Photo data is binary (new format)")
                    else:
                        logger.info("⚠️ Photo data is base64 (legacy format)")
                else:
                    logger.error("❌ Photo file data missing")
                    return False
            
            # Test certificate retrieval method
            logger.info("🔄 Testing certificate retrieval...")
            for edu in cv.education:
                for cert_id in edu.get('certificates', []):
                    cert_data = await pdf_service._get_certificate_data(cert_id, session)
                    if cert_data:
                        logger.info(f"✅ Certificate data retrieved: {cert_data['name']}")
                        
                        # Test binary data handling
                        if isinstance(cert_data['data'], bytes):
                            logger.info("✅ Certificate data is binary")
                        else:
                            logger.error("❌ Certificate data is not binary")
                            return False
                    else:
                        logger.error(f"❌ Certificate data not found: {cert_id}")
                        return False
            
            logger.info("✅ PDF service file handling tests passed!")
            return True
            
    except Exception as e:
        logger.error(f"❌ PDF service test failed: {e}")
        return False
    finally:
        await engine.dispose()


async def main():
    """Run all PDF generation tests."""
    logger.info("🚀 Starting PDF generation tests...")
    
    # Test 1: Basic PDF generation
    test1_result = await test_pdf_generation()
    
    # Test 2: PDF service methods
    test2_result = await test_pdf_service_methods()
    
    if test1_result and test2_result:
        logger.info("\n🎉 All PDF generation tests passed!")
        logger.info("📁 Check the generated PDF files:")
        logger.info("   - test_output_standard.pdf")
        logger.info("   - test_output_modern.pdf") 
        logger.info("   - test_output_german-ausbildung.pdf")
        logger.info("\n🔍 Open these files to verify that:")
        logger.info("   📸 Profile photos are displayed correctly")
        logger.info("   📜 Certificates are included in the German template")
        logger.info("   🎨 Colored skill indicators are working")
        logger.info("   📝 Proper filename generation is working")
        return 0
    else:
        logger.error("\n❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
